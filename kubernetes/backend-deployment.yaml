apiVersion: apps/v1
kind: Deployment
metadata:
  name: retreat-backend
  namespace: retreat-and-be
  labels:
    app: retreat-backend
    tier: backend
    version: v3.0.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: retreat-backend
  template:
    metadata:
      labels:
        app: retreat-backend
        tier: backend
        version: v3.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3000"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: backend
        image: retreat-backend:3.0.0
        ports:
        - containerPort: 3000
          name: http
        - containerPort: 9464
          name: metrics
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3000"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: retreat-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: retreat-secrets
              key: redis-url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: retreat-secrets
              key: jwt-secret
        - name: MONITORING_PROMETHEUS_ENABLED
          value: "true"
        - name: TRACING_ENABLED
          value: "true"
        - name: HANUMAN_BRIDGE_ENABLED
          value: "true"
        - name: ML_ANALYTICS_ENABLED
          value: "true"
        resources:
          requests:
            cpu: 200m
            memory: 256Mi
          limits:
            cpu: 1000m
            memory: 1Gi
        livenessProbe:
          httpGet:
            path: /health/live
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        - name: logs-volume
          mountPath: /app/logs
        - name: ml-models-volume
          mountPath: /app/models
      volumes:
      - name: config-volume
        configMap:
          name: retreat-config
      - name: logs-volume
        emptyDir: {}
      - name: ml-models-volume
        persistentVolumeClaim:
          claimName: ml-models-pvc
      imagePullSecrets:
      - name: retreat-registry-secret
---
apiVersion: v1
kind: Service
metadata:
  name: retreat-backend-service
  namespace: retreat-and-be
  labels:
    app: retreat-backend
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "3000"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  ports:
  - port: 3000
    targetPort: 3000
    protocol: TCP
    name: http
  - port: 9464
    targetPort: 9464
    protocol: TCP
    name: metrics
  selector:
    app: retreat-backend
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: retreat-backend-hpa
  namespace: retreat-and-be
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: retreat-backend
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: http_requests_per_second
      target:
        type: AverageValue
        averageValue: "100"
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Max
