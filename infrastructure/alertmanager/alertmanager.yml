global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'your-email-password'

route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default-receiver'
  routes:
    # Alertes critiques - notification immédiate
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 0s
      repeat_interval: 5m

    # Alertes sécurité - notification prioritaire
    - match:
        service: security
      receiver: 'security-alerts'
      group_wait: 5s
      repeat_interval: 15m

    # Alertes business - notification business hours
    - match:
        service: business
      receiver: 'business-alerts'
      group_wait: 30s
      repeat_interval: 4h

    # Alertes infrastructure - notification technique
    - match:
        service: infrastructure
      receiver: 'infrastructure-alerts'
      group_wait: 15s
      repeat_interval: 2h

receivers:
  - name: 'default-receiver'
    email_configs:
      - to: '<EMAIL>'
        subject: '[Retreat&Be] {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Labels: {{ range .Labels.SortedPairs }}{{ .Name }}={{ .Value }} {{ end }}
          {{ end }}

  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '🚨 [CRITICAL] {{ .GroupLabels.alertname }}'
        body: |
          🚨 ALERTE CRITIQUE 🚨
          
          {{ range .Alerts }}
          Service: {{ .Labels.service }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Instance: {{ .Labels.instance }}
          Time: {{ .StartsAt }}
          {{ end }}
          
          Action immédiate requise !
    slack_configs:
      - api_url: 'YOUR_SLACK_WEBHOOK_URL'
        channel: '#alerts-critical'
        title: '🚨 CRITICAL ALERT'
        text: |
          {{ range .Alerts }}
          *{{ .Annotations.summary }}*
          {{ .Annotations.description }}
          {{ end }}

  - name: 'security-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '🛡️ [SECURITY] {{ .GroupLabels.alertname }}'
        body: |
          🛡️ ALERTE SÉCURITÉ 🛡️
          
          {{ range .Alerts }}
          Type: {{ .Labels.alertname }}
          Description: {{ .Annotations.description }}
          Instance: {{ .Labels.instance }}
          Time: {{ .StartsAt }}
          {{ end }}
          
          Vérification sécurité recommandée.

  - name: 'business-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '📊 [BUSINESS] {{ .GroupLabels.alertname }}'
        body: |
          📊 ALERTE BUSINESS 📊
          
          {{ range .Alerts }}
          Métrique: {{ .Annotations.summary }}
          Impact: {{ .Annotations.description }}
          Time: {{ .StartsAt }}
          {{ end }}
          
          Analyse business recommandée.

  - name: 'infrastructure-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '⚙️ [INFRA] {{ .GroupLabels.alertname }}'
        body: |
          ⚙️ ALERTE INFRASTRUCTURE ⚙️
          
          {{ range .Alerts }}
          Composant: {{ .Labels.service }}
          Problème: {{ .Annotations.summary }}
          Détails: {{ .Annotations.description }}
          Instance: {{ .Labels.instance }}
          Time: {{ .StartsAt }}
          {{ end }}
          
          Intervention technique requise.

inhibit_rules:
  # Inhiber les alertes de warning si une alerte critique existe
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'instance']

  # Inhiber les alertes d'application si l'infrastructure est down
  - source_match:
      alertname: 'ApplicationDown'
    target_match:
      service: 'retreat-phase2'
    equal: ['instance']
