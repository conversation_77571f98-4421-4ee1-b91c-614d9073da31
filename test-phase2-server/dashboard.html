<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phase 2 Dashboard - Gap Analysis</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card h3 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
        }

        .card h3::before {
            content: '';
            width: 4px;
            height: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            margin-right: 10px;
            border-radius: 2px;
        }

        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .metric:last-child {
            border-bottom: none;
        }

        .metric-label {
            font-weight: 500;
            color: #4a5568;
        }

        .metric-value {
            font-weight: bold;
            color: #2d3748;
            font-size: 1.1rem;
        }

        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .status.healthy {
            background: #c6f6d5;
            color: #22543d;
        }

        .status.warning {
            background: #fef5e7;
            color: #c05621;
        }

        .status.error {
            background: #fed7d7;
            color: #c53030;
        }

        .logs {
            background: #1a202c;
            color: #e2e8f0;
            border-radius: 15px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 400px;
            overflow-y: auto;
        }

        .log-entry {
            margin-bottom: 8px;
            padding: 5px;
            border-radius: 3px;
        }

        .log-info {
            color: #63b3ed;
        }

        .log-warn {
            color: #f6ad55;
            background: rgba(246, 173, 85, 0.1);
        }

        .log-error {
            color: #fc8181;
            background: rgba(252, 129, 129, 0.1);
        }

        .actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .auto-refresh {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.9);
            padding: 10px 15px;
            border-radius: 25px;
            font-size: 0.9rem;
            color: #4a5568;
        }

        .refresh-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: #48bb78;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="auto-refresh">
        <span class="refresh-indicator"></span>
        Auto-refresh: 5s
    </div>

    <div class="container">
        <div class="header">
            <h1>🎯 Phase 2 Dashboard</h1>
            <p>Gap Analysis - Monitoring & Observabilité</p>
        </div>

        <div class="dashboard">
            <div class="card">
                <h3>🏥 Health Status</h3>
                <div id="health-metrics">
                    <div class="metric">
                        <span class="metric-label">Statut Global</span>
                        <span class="metric-value" id="global-status">Chargement...</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Uptime</span>
                        <span class="metric-value" id="uptime">-</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Mémoire</span>
                        <span class="metric-value" id="memory">-</span>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3>📊 Métriques HTTP</h3>
                <div id="http-metrics">
                    <div class="metric">
                        <span class="metric-label">Requêtes Totales</span>
                        <span class="metric-value" id="total-requests">-</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Temps Moyen</span>
                        <span class="metric-value" id="avg-response-time">-</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Dernière Requête</span>
                        <span class="metric-value" id="last-request">-</span>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3>🔒 Sécurité</h3>
                <div id="security-metrics">
                    <div class="metric">
                        <span class="metric-label">Tentatives XSS</span>
                        <span class="metric-value" id="xss-attempts">0</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Validations</span>
                        <span class="metric-value" id="validations">-</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Correlation IDs</span>
                        <span class="metric-value" id="correlation-ids">Actifs</span>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3>🎯 Business</h3>
                <div id="business-metrics">
                    <div class="metric">
                        <span class="metric-label">Registrations</span>
                        <span class="metric-value" id="registrations">-</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Health Score</span>
                        <span class="metric-value" id="health-score">-</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Services</span>
                        <span class="metric-value" id="services-count">3/3</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <h3>📝 Logs Temps Réel</h3>
            <div class="logs" id="logs-container">
                <div class="log-entry log-info">Initialisation du dashboard...</div>
            </div>
        </div>

        <div class="actions">
            <a href="http://localhost:3001/health" class="btn btn-primary" target="_blank">
                🏥 Health Check
            </a>
            <a href="http://localhost:3001/metrics" class="btn btn-secondary" target="_blank">
                📊 Métriques Prometheus
            </a>
            <button class="btn btn-secondary" onclick="testValidation()">
                🔒 Test Validation
            </button>
            <button class="btn btn-secondary" onclick="testTracing()">
                🔍 Test Tracing
            </button>
        </div>
    </div>

    <script>
        let requestCount = 0;
        let xssAttempts = 0;
        let validationTests = 0;

        // Fonction pour formater l'uptime
        function formatUptime(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = Math.floor(seconds % 60);
            return `${hours}h ${minutes}m ${secs}s`;
        }

        // Fonction pour formater la mémoire
        function formatMemory(bytes) {
            return `${Math.round(bytes / 1024 / 1024)}MB`;
        }

        // Fonction pour ajouter un log
        function addLog(message, type = 'info') {
            const logsContainer = document.getElementById('logs-container');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            
            logsContainer.appendChild(logEntry);
            logsContainer.scrollTop = logsContainer.scrollHeight;
            
            // Garder seulement les 50 derniers logs
            while (logsContainer.children.length > 50) {
                logsContainer.removeChild(logsContainer.firstChild);
            }
        }

        // Fonction pour récupérer les métriques
        async function fetchMetrics() {
            try {
                // Health check
                const healthResponse = await fetch('http://localhost:3001/health');
                const healthData = await healthResponse.json();
                
                document.getElementById('global-status').innerHTML = 
                    `<span class="status healthy">${healthData.status}</span>`;
                document.getElementById('uptime').textContent = formatUptime(healthData.uptime);
                document.getElementById('memory').textContent = formatMemory(healthData.memory.rss);
                
                requestCount++;
                document.getElementById('total-requests').textContent = requestCount;
                document.getElementById('last-request').textContent = new Date().toLocaleTimeString();
                
                // Métriques business
                document.getElementById('health-score').textContent = '100/100';
                document.getElementById('services-count').textContent = 
                    `${Object.keys(healthData.services).length}/${Object.keys(healthData.services).length}`;
                
                addLog(`Health check réussi - Status: ${healthData.status}`);
                
            } catch (error) {
                addLog(`Erreur lors de la récupération des métriques: ${error.message}`, 'error');
                document.getElementById('global-status').innerHTML = 
                    `<span class="status error">Erreur</span>`;
            }
        }

        // Test de validation
        async function testValidation() {
            try {
                const response = await fetch('http://localhost:3001/api/test-validation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        name: '<script>alert("xss")</script>',
                        email: '<EMAIL>'
                    })
                });
                
                validationTests++;
                
                if (response.status === 400) {
                    xssAttempts++;
                    document.getElementById('xss-attempts').textContent = xssAttempts;
                    addLog('Test XSS bloqué avec succès ✅', 'warn');
                } else {
                    addLog('Test de validation réussi ✅');
                }
                
                document.getElementById('validations').textContent = validationTests;
                
            } catch (error) {
                addLog(`Erreur test validation: ${error.message}`, 'error');
            }
        }

        // Test de tracing
        async function testTracing() {
            try {
                const response = await fetch('http://localhost:3001/api/test-tracing');
                const data = await response.json();
                
                addLog(`Test tracing réussi - TraceID: ${data.traceId.substring(0, 8)}...`);
                
            } catch (error) {
                addLog(`Erreur test tracing: ${error.message}`, 'error');
            }
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            addLog('Dashboard Phase 2 initialisé 🚀');
            fetchMetrics();
            
            // Auto-refresh toutes les 5 secondes
            setInterval(fetchMetrics, 5000);
        });
    </script>
</body>
</html>
