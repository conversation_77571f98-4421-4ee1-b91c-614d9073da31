config:
  target: 'http://localhost:3001'
  phases:
    # Phase de montée en charge progressive
    - duration: 60
      arrivalRate: 1
      name: "Warm up"
    - duration: 120
      arrivalRate: 5
      rampTo: 20
      name: "Ramp up load"
    - duration: 300
      arrivalRate: 20
      name: "Sustained load"
    - duration: 120
      arrivalRate: 20
      rampTo: 50
      name: "Peak load"
    - duration: 60
      arrivalRate: 50
      rampTo: 1
      name: "Ramp down"
  
  # Configuration des métriques
  metrics:
    - name: "response_time_p95"
      unit: "ms"
    - name: "response_time_p99"
      unit: "ms"
    - name: "error_rate"
      unit: "percent"

  # Variables d'environnement
  variables:
    correlationId:
      - "test-load-001"
      - "test-load-002"
      - "test-load-003"
    userAgent:
      - "Artillery Load Test"
      - "Performance Test Bot"

  # Configuration des plugins
  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true
    publish-metrics:
      - type: prometheus
        pushgateway: 'http://localhost:9091'
        prefix: 'artillery_'
    expect:
      outputFormat: 'json'
      reportFailuresAsErrors: true

  # Headers par défaut
  defaults:
    headers:
      'User-Agent': 'Artillery Load Test'
      'Accept': 'application/json'
      'Content-Type': 'application/json'

scenarios:
  # Test des health checks
  - name: "Health Check Load Test"
    weight: 30
    flow:
      - get:
          url: "/health"
          name: "health_check"
          headers:
            X-Correlation-ID: "{{ correlationId }}"
          expect:
            - statusCode: 200
            - hasProperty: "status"
            - equals:
                - "{{ status }}"
                - "healthy"
          capture:
            - json: "$.uptime"
              as: "uptime"
            - json: "$.memory.rss"
              as: "memory_usage"

      - get:
          url: "/health/live"
          name: "liveness_probe"
          expect:
            - statusCode: 200
            - contentType: json

      - get:
          url: "/health/ready"
          name: "readiness_probe"
          expect:
            - statusCode: 200

  # Test des métriques Prometheus
  - name: "Metrics Load Test"
    weight: 20
    flow:
      - get:
          url: "/metrics"
          name: "prometheus_metrics"
          expect:
            - statusCode: 200
            - contentType: "text/plain"
            - match: "http_requests_total"

      - get:
          url: "/metrics/business"
          name: "business_metrics"
          expect:
            - statusCode: 200
            - contentType: json

  # Test de validation avec charge
  - name: "Validation Load Test"
    weight: 25
    flow:
      - post:
          url: "/api/test-validation"
          name: "validation_success"
          json:
            name: "LoadTest{{ $randomString() }}"
            email: "test{{ $randomInt(1, 1000) }}@example.com"
          expect:
            - statusCode: 200
            - hasProperty: "message"

      - post:
          url: "/api/test-validation"
          name: "validation_xss_blocked"
          json:
            name: "<script>alert('xss')</script>"
            email: "<EMAIL>"
          expect:
            - statusCode: 400
            - hasProperty: "errors"

      - post:
          url: "/api/test-validation"
          name: "validation_invalid_email"
          json:
            name: "Test User"
            email: "invalid-email"
          expect:
            - statusCode: 400

  # Test de tracing sous charge
  - name: "Tracing Load Test"
    weight: 15
    flow:
      - get:
          url: "/api/test-tracing"
          name: "tracing_test"
          headers:
            X-Correlation-ID: "load-test-{{ $randomString() }}"
          expect:
            - statusCode: 200
            - hasProperty: "traceId"
            - hasProperty: "correlationId"
          capture:
            - json: "$.traceId"
              as: "traceId"

  # Test de stress avec erreurs intentionnelles
  - name: "Error Handling Load Test"
    weight: 10
    flow:
      - get:
          url: "/api/nonexistent-endpoint"
          name: "404_error_test"
          expect:
            - statusCode: 404

      - post:
          url: "/api/test-validation"
          name: "malformed_json_test"
          body: "{ invalid json }"
          expect:
            - statusCode: 400

# Configuration des hooks
before:
  flow:
    - log: "Starting load test for Phase 2 Gap Analysis"

after:
  flow:
    - log: "Load test completed"

# Configuration des rapports
reporting:
  output:
    - json
    - html
  
  # Seuils d'alerte
  thresholds:
    http.response_time:
      p95: 500  # 95% des requêtes < 500ms
      p99: 1000 # 99% des requêtes < 1s
    http.request_rate: 50  # Au moins 50 req/s
    http.codes.200: 0.95   # 95% de succès minimum
    http.codes.4xx: 0.05   # Maximum 5% d'erreurs client
    http.codes.5xx: 0.01   # Maximum 1% d'erreurs serveur
