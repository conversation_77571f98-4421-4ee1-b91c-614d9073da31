apiVersion: apps/v1
kind: Deployment
metadata:
  name: hanuman-bridge
  namespace: retreat-and-be
  labels:
    app: hanuman-bridge
    tier: middleware
    component: ai-orchestration
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: hanuman-bridge
  template:
    metadata:
      labels:
        app: hanuman-bridge
        tier: middleware
        component: ai-orchestration
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3001"
        prometheus.io/path: "/hanuman/metrics"
    spec:
      containers:
      - name: hanuman-bridge
        image: hanuman-bridge:3.0.0
        ports:
        - containerPort: 3001
          name: http
        - containerPort: 3002
          name: websocket
        - containerPort: 9465
          name: metrics
        env:
        - name: NODE_ENV
          value: "production"
        - name: HANUMAN_PORT
          value: "3001"
        - name: WEBSOCKET_PORT
          value: "3002"
        - name: RABBITMQ_URL
          valueFrom:
            secretKeyRef:
              name: retreat-secrets
              key: rabbitmq-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: retreat-secrets
              key: redis-url
        - name: HANUMAN_MAX_AGENTS_PER_TYPE
          value: "10"
        - name: HANUMAN_HEARTBEAT_INTERVAL
          value: "30000"
        - name: HANUMAN_TASK_TIMEOUT
          value: "300000"
        resources:
          requests:
            cpu: 150m
            memory: 256Mi
          limits:
            cpu: 500m
            memory: 512Mi
        livenessProbe:
          httpGet:
            path: /hanuman/status
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /hanuman/status
            port: 3001
          initialDelaySeconds: 10
          periodSeconds: 5
        volumeMounts:
        - name: hanuman-config
          mountPath: /app/config/hanuman
        - name: agent-logs
          mountPath: /app/logs/agents
      volumes:
      - name: hanuman-config
        configMap:
          name: hanuman-config
      - name: agent-logs
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: hanuman-bridge-service
  namespace: retreat-and-be
  labels:
    app: hanuman-bridge
spec:
  type: ClusterIP
  ports:
  - port: 3001
    targetPort: 3001
    protocol: TCP
    name: http
  - port: 3002
    targetPort: 3002
    protocol: TCP
    name: websocket
  selector:
    app: hanuman-bridge
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: hanuman-config
  namespace: retreat-and-be
data:
  agents.yaml: |
    security:
      maxInstances: 3
      capabilities:
        - vulnerability_scan
        - compliance_check
        - intrusion_detection
      configuration:
        scanInterval: 300000
        alertThresholds:
          critical: 1
          high: 5
          medium: 10
    
    performance:
      maxInstances: 2
      capabilities:
        - metrics_analysis
        - optimization
        - resource_monitoring
      configuration:
        monitoringInterval: 60000
        thresholds:
          cpu: 80
          memory: 85
          disk: 90
    
    qa:
      maxInstances: 2
      capabilities:
        - test_execution
        - quality_assessment
        - regression_testing
      configuration:
        testInterval: 1800000
        testSuites:
          - unit
          - integration
          - e2e
    
    devops:
      maxInstances: 1
      capabilities:
        - deployment
        - infrastructure_management
        - monitoring_setup
      configuration:
        deploymentMode: blue-green
        rollbackThreshold: 5
    
    analytics:
      maxInstances: 1
      capabilities:
        - data_analysis
        - ml_predictions
        - business_intelligence
      configuration:
        analysisInterval: 3600000
        mlModels:
          - conversion
          - churn
          - recommendation
