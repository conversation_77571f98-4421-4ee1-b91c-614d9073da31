#!/bin/bash

# Script de démarrage infrastructure production
# Option A : Intégration Production

set -e

echo "🏗️ Démarrage Infrastructure Production - Option A"
echo "================================================"

# Couleurs
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# Vérifier Docker
check_docker() {
    log_info "Vérification de Docker..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker n'est pas installé"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose n'est pas installé"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker daemon n'est pas en cours d'exécution"
        exit 1
    fi
    
    log_success "Docker vérifié"
}

# Créer les répertoires nécessaires
create_directories() {
    log_info "Création des répertoires..."
    
    mkdir -p infrastructure/prometheus/rules
    mkdir -p infrastructure/grafana/dashboards
    mkdir -p infrastructure/grafana/provisioning/datasources
    mkdir -p infrastructure/grafana/provisioning/dashboards
    mkdir -p infrastructure/alertmanager
    mkdir -p logs
    
    log_success "Répertoires créés"
}

# Arrêter les services existants
stop_existing_services() {
    log_info "Arrêt des services existants..."
    
    cd infrastructure
    docker-compose -f docker-compose.monitoring.yml down --remove-orphans 2>/dev/null || true
    cd ..
    
    log_success "Services existants arrêtés"
}

# Démarrer l'infrastructure
start_infrastructure() {
    log_info "Démarrage de l'infrastructure de monitoring..."
    
    cd infrastructure
    
    # Démarrer les services
    docker-compose -f docker-compose.monitoring.yml up -d
    
    cd ..
    
    log_success "Infrastructure démarrée"
}

# Vérifier les services
check_services() {
    log_info "Vérification des services..."
    
    # Attendre que les services démarrent
    sleep 30
    
    # Vérifier Prometheus
    if curl -s http://localhost:9090/-/healthy > /dev/null; then
        log_success "Prometheus: ✅ http://localhost:9090"
    else
        log_warning "Prometheus: ⚠️ En cours de démarrage..."
    fi
    
    # Vérifier Grafana
    if curl -s http://localhost:3000/api/health > /dev/null; then
        log_success "Grafana: ✅ http://localhost:3000 (admin/admin123)"
    else
        log_warning "Grafana: ⚠️ En cours de démarrage..."
    fi
    
    # Vérifier AlertManager
    if curl -s http://localhost:9093/-/healthy > /dev/null; then
        log_success "AlertManager: ✅ http://localhost:9093"
    else
        log_warning "AlertManager: ⚠️ En cours de démarrage..."
    fi
    
    # Vérifier Jaeger
    if curl -s http://localhost:16686/api/services > /dev/null; then
        log_success "Jaeger: ✅ http://localhost:16686"
    else
        log_warning "Jaeger: ⚠️ En cours de démarrage..."
    fi
    
    # Vérifier Redis
    if docker exec retreat-redis redis-cli ping 2>/dev/null | grep -q PONG; then
        log_success "Redis: ✅ localhost:6379"
    else
        log_warning "Redis: ⚠️ En cours de démarrage..."
    fi
}

# Configurer Grafana
configure_grafana() {
    log_info "Configuration de Grafana..."
    
    # Attendre que Grafana soit prêt
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -s http://localhost:3000/api/health > /dev/null; then
            break
        fi
        sleep 2
        ((timeout-=2))
    done
    
    if [ $timeout -le 0 ]; then
        log_warning "Grafana n'est pas encore prêt, configuration manuelle requise"
        return
    fi
    
    # Importer des dashboards par défaut
    log_info "Import des dashboards Grafana..."
    
    # Dashboard Node Exporter
    curl -s -X POST \
        -H "Content-Type: application/json" \
        -d '{"dashboard":{"id":1860,"version":27,"title":"Node Exporter Full"},"overwrite":true}' \
        ************************************/api/dashboards/import > /dev/null || true
    
    log_success "Grafana configuré"
}

# Tester l'intégration
test_integration() {
    log_info "Test de l'intégration..."
    
    # Tester que Prometheus collecte les métriques de Phase 2
    if curl -s "http://localhost:9090/api/v1/query?query=up{job=\"retreat-phase2\"}" | grep -q "success"; then
        log_success "Prometheus collecte les métriques Phase 2"
    else
        log_warning "Métriques Phase 2 non détectées (serveur Phase 2 requis)"
    fi
    
    # Tester les alertes
    if curl -s http://localhost:9093/api/v1/alerts | grep -q "alerts"; then
        log_success "AlertManager opérationnel"
    else
        log_warning "AlertManager en cours d'initialisation"
    fi
}

# Afficher les informations de déploiement
show_deployment_info() {
    echo ""
    log_success "🎉 Infrastructure Production déployée avec succès !"
    echo ""
    log_info "📊 Services disponibles :"
    echo "  🔍 Prometheus:    http://localhost:9090"
    echo "  📈 Grafana:       http://localhost:3000 (admin/admin123)"
    echo "  🚨 AlertManager:  http://localhost:9093"
    echo "  🔍 Jaeger:        http://localhost:16686"
    echo "  💾 Redis:         localhost:6379"
    echo "  📊 Node Exporter: http://localhost:9100"
    echo "  🐳 cAdvisor:      http://localhost:8080"
    echo ""
    log_info "🎯 Prochaines étapes :"
    echo "  1. Configurer les dashboards Grafana"
    echo "  2. Tester les alertes"
    echo "  3. Intégrer avec l'application Phase 2"
    echo "  4. Configurer les notifications (email/Slack)"
    echo ""
    log_info "📚 Documentation :"
    echo "  • Prometheus: http://localhost:9090/graph"
    echo "  • Grafana dashboards: http://localhost:3000/dashboards"
    echo "  • Alertes: http://localhost:9093/#/alerts"
    echo ""
    log_info "🔧 Commandes utiles :"
    echo "  # Voir les logs"
    echo "  docker-compose -f infrastructure/docker-compose.monitoring.yml logs -f"
    echo ""
    echo "  # Redémarrer un service"
    echo "  docker-compose -f infrastructure/docker-compose.monitoring.yml restart grafana"
    echo ""
    echo "  # Arrêter l'infrastructure"
    echo "  docker-compose -f infrastructure/docker-compose.monitoring.yml down"
}

# Fonction principale
main() {
    check_docker
    create_directories
    stop_existing_services
    start_infrastructure
    check_services
    configure_grafana
    test_integration
    show_deployment_info
}

# Gestion des erreurs
trap 'log_error "Erreur lors du déploiement. Vérifiez les logs Docker."' ERR

# Exécuter
main "$@"
