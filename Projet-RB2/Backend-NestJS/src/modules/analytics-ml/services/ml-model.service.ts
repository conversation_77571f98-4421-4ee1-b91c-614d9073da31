import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import * as tf from '@tensorflow/tfjs-node';
import { StructuredLoggerService } from '../../logging/structured-logger.service';
import { PrometheusService } from '../../monitoring/prometheus.service';

interface ModelMetrics {
  accuracy: number;
  loss: number;
  lastTrained: Date;
  trainingTime: number;
  predictions: number;
  version: string;
}

interface TrainingData {
  features: number[][];
  labels: number[][];
  validationSplit?: number;
}

/**
 * Service de gestion des modèles ML avec TensorFlow.js
 */
@Injectable()
export class MLModelService implements OnModuleInit {
  private readonly logger = new Logger(MLModelService.name);
  private models = new Map<string, tf.LayersModel>();
  private modelMetrics = new Map<string, ModelMetrics>();
  private isInitialized = false;

  constructor(
    private readonly configService: ConfigService,
    private readonly structuredLogger: StructuredLoggerService,
    private readonly prometheusService: PrometheusService,
  ) {}

  async onModuleInit() {
    await this.initializeModels();
  }

  /**
   * Initialiser tous les modèles ML
   */
  private async initializeModels() {
    try {
      this.logger.log('Initializing ML models...');

      // Modèle de prédiction de conversion
      await this.createConversionModel();
      
      // Modèle de détection d'anomalies
      await this.createAnomalyModel();
      
      // Modèle de recommandation
      await this.createRecommendationModel();
      
      // Modèle de prédiction de churn
      await this.createChurnModel();

      // Charger les modèles pré-entraînés si disponibles
      await this.loadPretrainedModels();

      this.isInitialized = true;
      this.logger.log('All ML models initialized successfully');

      // Métriques Prometheus
      this.prometheusService.recordMLEvent('models_initialized', {
        model_count: this.models.size.toString(),
      });

    } catch (error) {
      this.logger.error('Failed to initialize ML models', error);
      throw error;
    }
  }

  /**
   * Créer le modèle de prédiction de conversion
   */
  private async createConversionModel() {
    const model = tf.sequential({
      layers: [
        tf.layers.dense({
          inputShape: [25], // 25 features utilisateur
          units: 128,
          activation: 'relu',
          kernelInitializer: 'heNormal',
        }),
        tf.layers.dropout({ rate: 0.3 }),
        tf.layers.dense({
          units: 64,
          activation: 'relu',
          kernelInitializer: 'heNormal',
        }),
        tf.layers.dropout({ rate: 0.2 }),
        tf.layers.dense({
          units: 32,
          activation: 'relu',
        }),
        tf.layers.dense({
          units: 1,
          activation: 'sigmoid', // Probabilité de conversion
        }),
      ],
    });

    model.compile({
      optimizer: tf.train.adam(0.001),
      loss: 'binaryCrossentropy',
      metrics: ['accuracy', 'precision', 'recall'],
    });

    this.models.set('conversion', model);
    this.modelMetrics.set('conversion', {
      accuracy: 0,
      loss: 0,
      lastTrained: new Date(),
      trainingTime: 0,
      predictions: 0,
      version: '1.0.0',
    });

    this.logger.log('Conversion prediction model created');
  }

  /**
   * Créer le modèle de détection d'anomalies
   */
  private async createAnomalyModel() {
    // Autoencoder pour détection d'anomalies
    const encoder = tf.sequential({
      layers: [
        tf.layers.dense({
          inputShape: [20], // 20 métriques système
          units: 16,
          activation: 'relu',
        }),
        tf.layers.dense({
          units: 8,
          activation: 'relu',
        }),
        tf.layers.dense({
          units: 4,
          activation: 'relu',
        }),
      ],
    });

    const decoder = tf.sequential({
      layers: [
        tf.layers.dense({
          inputShape: [4],
          units: 8,
          activation: 'relu',
        }),
        tf.layers.dense({
          units: 16,
          activation: 'relu',
        }),
        tf.layers.dense({
          units: 20,
          activation: 'linear',
        }),
      ],
    });

    const autoencoder = tf.sequential({
      layers: [encoder, decoder],
    });

    autoencoder.compile({
      optimizer: tf.train.adam(0.001),
      loss: 'meanSquaredError',
      metrics: ['mae'],
    });

    this.models.set('anomaly', autoencoder);
    this.modelMetrics.set('anomaly', {
      accuracy: 0,
      loss: 0,
      lastTrained: new Date(),
      trainingTime: 0,
      predictions: 0,
      version: '1.0.0',
    });

    this.logger.log('Anomaly detection model created');
  }

  /**
   * Créer le modèle de recommandation
   */
  private async createRecommendationModel() {
    const model = tf.sequential({
      layers: [
        tf.layers.dense({
          inputShape: [30], // Features utilisateur + contexte
          units: 256,
          activation: 'relu',
        }),
        tf.layers.dropout({ rate: 0.4 }),
        tf.layers.dense({
          units: 128,
          activation: 'relu',
        }),
        tf.layers.dropout({ rate: 0.3 }),
        tf.layers.dense({
          units: 64,
          activation: 'relu',
        }),
        tf.layers.dense({
          units: 20, // 20 catégories de recommandations
          activation: 'softmax',
        }),
      ],
    });

    model.compile({
      optimizer: tf.train.adam(0.001),
      loss: 'categoricalCrossentropy',
      metrics: ['accuracy', 'topKCategoricalAccuracy'],
    });

    this.models.set('recommendation', model);
    this.modelMetrics.set('recommendation', {
      accuracy: 0,
      loss: 0,
      lastTrained: new Date(),
      trainingTime: 0,
      predictions: 0,
      version: '1.0.0',
    });

    this.logger.log('Recommendation model created');
  }

  /**
   * Créer le modèle de prédiction de churn
   */
  private async createChurnModel() {
    const model = tf.sequential({
      layers: [
        tf.layers.dense({
          inputShape: [22], // Features comportement utilisateur
          units: 96,
          activation: 'relu',
          kernelRegularizer: tf.regularizers.l2({ l2: 0.001 }),
        }),
        tf.layers.dropout({ rate: 0.4 }),
        tf.layers.dense({
          units: 48,
          activation: 'relu',
          kernelRegularizer: tf.regularizers.l2({ l2: 0.001 }),
        }),
        tf.layers.dropout({ rate: 0.3 }),
        tf.layers.dense({
          units: 24,
          activation: 'relu',
        }),
        tf.layers.dense({
          units: 1,
          activation: 'sigmoid', // Probabilité de churn
        }),
      ],
    });

    model.compile({
      optimizer: tf.train.adam(0.0005),
      loss: 'binaryCrossentropy',
      metrics: ['accuracy', 'precision', 'recall', 'auc'],
    });

    this.models.set('churn', model);
    this.modelMetrics.set('churn', {
      accuracy: 0,
      loss: 0,
      lastTrained: new Date(),
      trainingTime: 0,
      predictions: 0,
      version: '1.0.0',
    });

    this.logger.log('Churn prediction model created');
  }

  /**
   * Faire une prédiction avec un modèle
   */
  async predict(modelName: string, features: number[]): Promise<number[]> {
    if (!this.isInitialized) {
      throw new Error('ML models not initialized');
    }

    const model = this.models.get(modelName);
    if (!model) {
      throw new Error(`Model ${modelName} not found`);
    }

    try {
      const inputTensor = tf.tensor2d([features]);
      const prediction = model.predict(inputTensor) as tf.Tensor;
      const result = await prediction.data();

      // Nettoyer les tenseurs
      inputTensor.dispose();
      prediction.dispose();

      // Mettre à jour les métriques
      const metrics = this.modelMetrics.get(modelName);
      if (metrics) {
        metrics.predictions++;
        this.modelMetrics.set(modelName, metrics);
      }

      // Métriques Prometheus
      this.prometheusService.recordMLEvent('prediction_made', {
        model_name: modelName,
      });

      return Array.from(result);
    } catch (error) {
      this.logger.error(`Prediction failed for model ${modelName}`, error);
      throw error;
    }
  }

  /**
   * Entraîner un modèle
   */
  async trainModel(
    modelName: string,
    trainingData: TrainingData,
    epochs: number = 50
  ): Promise<ModelMetrics> {
    const model = this.models.get(modelName);
    if (!model) {
      throw new Error(`Model ${modelName} not found`);
    }

    try {
      this.logger.log(`Starting training for model ${modelName}`);
      const startTime = Date.now();

      const xs = tf.tensor2d(trainingData.features);
      const ys = tf.tensor2d(trainingData.labels);

      const history = await model.fit(xs, ys, {
        epochs,
        batchSize: 32,
        validationSplit: trainingData.validationSplit || 0.2,
        shuffle: true,
        callbacks: {
          onEpochEnd: (epoch, logs) => {
            if (epoch % 10 === 0) {
              this.logger.log(`Epoch ${epoch}: loss=${logs.loss.toFixed(4)}, accuracy=${logs.acc?.toFixed(4) || 'N/A'}`);
            }
          },
        },
      });

      // Nettoyer les tenseurs
      xs.dispose();
      ys.dispose();

      const trainingTime = Date.now() - startTime;
      const finalLoss = history.history.loss[history.history.loss.length - 1] as number;
      const finalAccuracy = history.history.acc ? 
        history.history.acc[history.history.acc.length - 1] as number : 0;

      // Mettre à jour les métriques
      const metrics: ModelMetrics = {
        accuracy: finalAccuracy,
        loss: finalLoss,
        lastTrained: new Date(),
        trainingTime,
        predictions: this.modelMetrics.get(modelName)?.predictions || 0,
        version: this.incrementVersion(this.modelMetrics.get(modelName)?.version || '1.0.0'),
      };

      this.modelMetrics.set(modelName, metrics);

      // Sauvegarder le modèle
      await this.saveModel(modelName);

      // Logs et métriques
      this.structuredLogger.logBusinessEvent(
        'model_trained',
        'ml_model',
        modelName,
        'train',
        'success',
        {
          epochs,
          trainingTime,
          finalAccuracy,
          finalLoss,
        }
      );

      this.prometheusService.recordMLEvent('model_trained', {
        model_name: modelName,
        accuracy: finalAccuracy.toString(),
      });

      this.logger.log(`Model ${modelName} trained successfully in ${trainingTime}ms`);
      return metrics;

    } catch (error) {
      this.logger.error(`Training failed for model ${modelName}`, error);
      throw error;
    }
  }

  /**
   * Obtenir les métriques d'un modèle
   */
  getModelMetrics(modelName: string): ModelMetrics | undefined {
    return this.modelMetrics.get(modelName);
  }

  /**
   * Obtenir toutes les métriques des modèles
   */
  getAllModelMetrics(): Record<string, ModelMetrics> {
    const result = {};
    for (const [name, metrics] of this.modelMetrics.entries()) {
      result[name] = metrics;
    }
    return result;
  }

  /**
   * Sauvegarder un modèle
   */
  private async saveModel(modelName: string): Promise<void> {
    try {
      const model = this.models.get(modelName);
      if (!model) return;

      const modelPath = `file://./models/${modelName}`;
      await model.save(modelPath);
      
      this.logger.log(`Model ${modelName} saved to ${modelPath}`);
    } catch (error) {
      this.logger.error(`Failed to save model ${modelName}`, error);
    }
  }

  /**
   * Charger les modèles pré-entraînés
   */
  private async loadPretrainedModels(): Promise<void> {
    const modelNames = ['conversion', 'anomaly', 'recommendation', 'churn'];
    
    for (const modelName of modelNames) {
      try {
        const modelPath = `file://./models/${modelName}/model.json`;
        const loadedModel = await tf.loadLayersModel(modelPath);
        
        this.models.set(modelName, loadedModel);
        this.logger.log(`Pre-trained model ${modelName} loaded`);
      } catch (error) {
        // Modèle pré-entraîné non disponible, utiliser le modèle nouvellement créé
        this.logger.log(`No pre-trained model found for ${modelName}, using new model`);
      }
    }
  }

  /**
   * Incrémenter la version d'un modèle
   */
  private incrementVersion(currentVersion: string): string {
    const parts = currentVersion.split('.');
    const patch = parseInt(parts[2]) + 1;
    return `${parts[0]}.${parts[1]}.${patch}`;
  }

  /**
   * Tâche de maintenance automatique
   */
  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async performMaintenanceTasks() {
    this.logger.log('Performing ML model maintenance...');

    // Nettoyer la mémoire GPU/CPU
    tf.disposeVariables();

    // Sauvegarder tous les modèles
    for (const modelName of this.models.keys()) {
      await this.saveModel(modelName);
    }

    // Log des métriques
    const allMetrics = this.getAllModelMetrics();
    this.structuredLogger.logBusinessEvent(
      'ml_maintenance_completed',
      'ml_system',
      'maintenance',
      'complete',
      'success',
      { modelCount: Object.keys(allMetrics).length, metrics: allMetrics }
    );

    this.logger.log('ML model maintenance completed');
  }

  /**
   * Obtenir les informations système TensorFlow
   */
  getTensorFlowInfo() {
    return {
      version: tf.version.tfjs,
      backend: tf.getBackend(),
      memory: tf.memory(),
      environment: tf.env(),
      modelCount: this.models.size,
      isInitialized: this.isInitialized,
    };
  }
}
