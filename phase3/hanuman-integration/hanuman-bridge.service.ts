import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { WebSocketGateway, WebSocketServer, SubscribeMessage } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import * as amqp from 'amqplib';

/**
 * Service d'intégration avancée avec Hanuman
 * Communication bidirectionnelle temps réel
 */
@Injectable()
@WebSocketGateway({
  cors: {
    origin: '*',
  },
  namespace: '/hanuman-bridge'
})
export class HanumanBridgeService {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(HanumanBridgeService.name);
  private rabbitConnection: amqp.Connection;
  private rabbitChannel: amqp.Channel;
  private connectedAgents = new Map<string, Socket>();

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.initializeRabbitMQ();
  }

  /**
   * Initialiser la connexion RabbitMQ pour communication avec <PERSON>
   */
  private async initializeRabbitMQ() {
    try {
      const rabbitUrl = this.configService.get('RABBITMQ_URL', 'amqp://localhost');
      this.rabbitConnection = await amqp.connect(rabbitUrl);
      this.rabbitChannel = await this.rabbitConnection.createChannel();

      // Déclarer les exchanges et queues
      await this.rabbitChannel.assertExchange('hanuman.events', 'topic', { durable: true });
      await this.rabbitChannel.assertExchange('retreat.events', 'topic', { durable: true });

      // Queues pour communication bidirectionnelle
      await this.rabbitChannel.assertQueue('hanuman.commands', { durable: true });
      await this.rabbitChannel.assertQueue('hanuman.responses', { durable: true });
      await this.rabbitChannel.assertQueue('retreat.notifications', { durable: true });

      // Écouter les messages de Hanuman
      await this.rabbitChannel.consume('hanuman.responses', this.handleHanumanMessage.bind(this));

      this.logger.log('RabbitMQ connection established for Hanuman integration');
    } catch (error) {
      this.logger.error('Failed to initialize RabbitMQ', error);
    }
  }

  /**
   * Gérer les connexions WebSocket des agents Hanuman
   */
  @SubscribeMessage('agent.register')
  handleAgentRegistration(client: Socket, payload: any) {
    const { agentId, agentType, capabilities } = payload;
    
    this.connectedAgents.set(agentId, client);
    client.data.agentId = agentId;
    client.data.agentType = agentType;
    client.data.capabilities = capabilities;

    this.logger.log(`Hanuman agent registered: ${agentId} (${agentType})`);

    // Notifier les autres services
    this.eventEmitter.emit('hanuman.agent.connected', {
      agentId,
      agentType,
      capabilities,
      timestamp: new Date(),
    });

    // Envoyer la confirmation
    client.emit('agent.registered', {
      success: true,
      agentId,
      assignedTasks: this.getAssignedTasks(agentType),
    });
  }

  /**
   * Traiter les commandes pour les agents Hanuman
   */
  @SubscribeMessage('command.execute')
  async handleCommand(client: Socket, payload: any) {
    const { commandId, command, parameters, targetAgent } = payload;
    const agentId = client.data.agentId;

    this.logger.log(`Command received from ${agentId}: ${command}`);

    try {
      // Valider la commande
      const validationResult = await this.validateCommand(command, parameters, agentId);
      if (!validationResult.valid) {
        client.emit('command.error', {
          commandId,
          error: validationResult.error,
        });
        return;
      }

      // Exécuter la commande
      const result = await this.executeCommand(command, parameters, agentId);

      // Envoyer le résultat
      client.emit('command.result', {
        commandId,
        result,
        timestamp: new Date(),
      });

      // Publier l'événement
      await this.publishEvent('hanuman.command.executed', {
        agentId,
        command,
        parameters,
        result,
        timestamp: new Date(),
      });

    } catch (error) {
      this.logger.error(`Command execution failed: ${error.message}`);
      client.emit('command.error', {
        commandId,
        error: error.message,
      });
    }
  }

  /**
   * Traiter les rapports d'analyse des agents
   */
  @SubscribeMessage('analysis.report')
  async handleAnalysisReport(client: Socket, payload: any) {
    const { reportId, analysisType, findings, recommendations, severity } = payload;
    const agentId = client.data.agentId;

    this.logger.log(`Analysis report received from ${agentId}: ${analysisType}`);

    // Stocker le rapport
    await this.storeAnalysisReport({
      reportId,
      agentId,
      analysisType,
      findings,
      recommendations,
      severity,
      timestamp: new Date(),
    });

    // Déclencher des actions basées sur la sévérité
    if (severity === 'critical') {
      await this.handleCriticalFindings(findings, agentId);
    }

    // Notifier les autres services
    this.eventEmitter.emit('hanuman.analysis.completed', {
      reportId,
      agentId,
      analysisType,
      severity,
      findingsCount: findings.length,
    });

    client.emit('analysis.acknowledged', {
      reportId,
      status: 'processed',
      nextActions: this.getNextActions(analysisType, severity),
    });
  }

  /**
   * Envoyer une tâche à un agent spécifique
   */
  async assignTaskToAgent(agentType: string, task: any): Promise<boolean> {
    const availableAgents = Array.from(this.connectedAgents.entries())
      .filter(([_, socket]) => socket.data.agentType === agentType);

    if (availableAgents.length === 0) {
      this.logger.warn(`No available agents of type: ${agentType}`);
      return false;
    }

    // Sélectionner l'agent le moins chargé
    const selectedAgent = this.selectOptimalAgent(availableAgents);
    const [agentId, socket] = selectedAgent;

    socket.emit('task.assigned', {
      taskId: task.id,
      type: task.type,
      parameters: task.parameters,
      priority: task.priority,
      deadline: task.deadline,
    });

    this.logger.log(`Task ${task.id} assigned to agent ${agentId}`);
    return true;
  }

  /**
   * Diffuser un événement à tous les agents connectés
   */
  async broadcastToAgents(event: string, data: any) {
    this.server.emit(event, data);
    
    // Publier aussi via RabbitMQ pour les agents non-WebSocket
    await this.publishEvent(`hanuman.broadcast.${event}`, data);
  }

  /**
   * Obtenir le statut de tous les agents connectés
   */
  getConnectedAgentsStatus() {
    const agents = Array.from(this.connectedAgents.entries()).map(([agentId, socket]) => ({
      agentId,
      agentType: socket.data.agentType,
      capabilities: socket.data.capabilities,
      connected: socket.connected,
      lastSeen: socket.data.lastSeen || new Date(),
    }));

    return {
      totalAgents: agents.length,
      agentsByType: this.groupAgentsByType(agents),
      agents,
    };
  }

  /**
   * Méthodes privées
   */
  private async handleHanumanMessage(msg: amqp.ConsumeMessage) {
    if (!msg) return;

    try {
      const content = JSON.parse(msg.content.toString());
      const { type, data, agentId } = content;

      switch (type) {
        case 'status_update':
          await this.handleAgentStatusUpdate(agentId, data);
          break;
        case 'task_completed':
          await this.handleTaskCompletion(agentId, data);
          break;
        case 'alert':
          await this.handleAgentAlert(agentId, data);
          break;
        default:
          this.logger.warn(`Unknown message type: ${type}`);
      }

      this.rabbitChannel.ack(msg);
    } catch (error) {
      this.logger.error('Error processing Hanuman message', error);
      this.rabbitChannel.nack(msg, false, false);
    }
  }

  private async validateCommand(command: string, parameters: any, agentId: string) {
    // Validation des commandes basée sur les permissions de l'agent
    const allowedCommands = this.getAllowedCommands(agentId);
    
    if (!allowedCommands.includes(command)) {
      return { valid: false, error: 'Command not allowed for this agent' };
    }

    // Validation des paramètres
    const paramValidation = this.validateParameters(command, parameters);
    if (!paramValidation.valid) {
      return paramValidation;
    }

    return { valid: true };
  }

  private async executeCommand(command: string, parameters: any, agentId: string) {
    switch (command) {
      case 'analyze_security':
        return await this.executeSecurityAnalysis(parameters);
      case 'optimize_performance':
        return await this.executePerformanceOptimization(parameters);
      case 'generate_report':
        return await this.generateReport(parameters);
      case 'update_configuration':
        return await this.updateConfiguration(parameters, agentId);
      default:
        throw new Error(`Unknown command: ${command}`);
    }
  }

  private async executeSecurityAnalysis(parameters: any) {
    // Intégration avec le système de sécurité Phase 2
    return {
      analysisId: `sec_${Date.now()}`,
      status: 'completed',
      findings: [],
      recommendations: [],
      score: 95,
    };
  }

  private async executePerformanceOptimization(parameters: any) {
    // Intégration avec le monitoring Prometheus
    return {
      optimizationId: `perf_${Date.now()}`,
      status: 'completed',
      improvements: [],
      metrics: {},
    };
  }

  private async publishEvent(routingKey: string, data: any) {
    if (!this.rabbitChannel) return;

    await this.rabbitChannel.publish(
      'hanuman.events',
      routingKey,
      Buffer.from(JSON.stringify(data)),
      { persistent: true }
    );
  }

  private selectOptimalAgent(agents: [string, Socket][]): [string, Socket] {
    // Sélection basée sur la charge et les performances
    return agents[0]; // Simplified for now
  }

  private getAssignedTasks(agentType: string) {
    // Retourner les tâches assignées basées sur le type d'agent
    const taskMap = {
      'security': ['vulnerability_scan', 'compliance_check'],
      'performance': ['metrics_analysis', 'optimization'],
      'qa': ['test_execution', 'quality_assessment'],
      'devops': ['deployment', 'infrastructure_management'],
    };

    return taskMap[agentType] || [];
  }

  private getAllowedCommands(agentId: string): string[] {
    // Retourner les commandes autorisées pour cet agent
    return ['analyze_security', 'optimize_performance', 'generate_report'];
  }

  private validateParameters(command: string, parameters: any) {
    // Validation spécifique par commande
    return { valid: true };
  }

  private groupAgentsByType(agents: any[]) {
    return agents.reduce((acc, agent) => {
      acc[agent.agentType] = (acc[agent.agentType] || 0) + 1;
      return acc;
    }, {});
  }

  private async storeAnalysisReport(report: any) {
    // Stocker le rapport en base de données
    this.logger.log(`Storing analysis report: ${report.reportId}`);
  }

  private async handleCriticalFindings(findings: any[], agentId: string) {
    // Gérer les découvertes critiques
    this.logger.warn(`Critical findings detected by agent ${agentId}`);
  }

  private getNextActions(analysisType: string, severity: string) {
    // Retourner les prochaines actions recommandées
    return [];
  }

  private async handleAgentStatusUpdate(agentId: string, data: any) {
    // Traiter les mises à jour de statut
    this.logger.log(`Agent ${agentId} status update: ${data.status}`);
  }

  private async handleTaskCompletion(agentId: string, data: any) {
    // Traiter la completion de tâche
    this.logger.log(`Task ${data.taskId} completed by agent ${agentId}`);
  }

  private async handleAgentAlert(agentId: string, data: any) {
    // Traiter les alertes d'agent
    this.logger.warn(`Alert from agent ${agentId}: ${data.message}`);
  }

  private async generateReport(parameters: any) {
    return {
      reportId: `report_${Date.now()}`,
      status: 'generated',
      url: '/reports/latest',
    };
  }

  private async updateConfiguration(parameters: any, agentId: string) {
    return {
      configId: `config_${Date.now()}`,
      status: 'updated',
      changes: parameters.changes || [],
    };
  }
}
