import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';
import { HanumanBridgeService } from './hanuman-bridge.service';
import { HanumanBridgeGateway } from './hanuman-bridge.gateway';
import { HanumanBridgeController } from './hanuman-bridge.controller';
import { AgentManagerService } from './services/agent-manager.service';
import { TaskOrchestratorService } from './services/task-orchestrator.service';
import { CommunicationService } from './services/communication.service';
import { WorkflowEngineService } from './services/workflow-engine.service';
import { MonitoringModule } from '../monitoring/monitoring.module';
import { LoggingModule } from '../logging/logging.module';

/**
 * Module Hanuman Bridge - Phase 3
 * Intégration avancée avec le système <PERSON>uman
 */
@Module({
  imports: [
    ConfigModule,
    EventEmitterModule,
    ScheduleModule.forRoot(),
    MonitoringModule,
    LoggingModule,
  ],
  providers: [
    HanumanBridgeService,
    HanumanBridgeGateway,
    AgentManagerService,
    TaskOrchestratorService,
    CommunicationService,
    WorkflowEngineService,
  ],
  controllers: [HanumanBridgeController],
  exports: [
    HanumanBridgeService,
    AgentManagerService,
    TaskOrchestratorService,
  ],
})
export class HanumanBridgeModule {}
