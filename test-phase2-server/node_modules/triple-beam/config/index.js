/**
 * index.js: Default settings for all levels that <PERSON><PERSON> knows about.
 *
 * (C) 2010 <PERSON>
 * MIT LICENCE
 */

'use strict';

/**
 * Export config set for the CLI.
 * @type {Object}
 */
Object.defineProperty(exports, 'cli', {
  value: require('./cli')
});

/**
 * Export config set for npm.
 * @type {Object}
 */
Object.defineProperty(exports, 'npm', {
  value: require('./npm')
});

/**
 * Export config set for the syslog.
 * @type {Object}
 */
Object.defineProperty(exports, 'syslog', {
  value: require('./syslog')
});
