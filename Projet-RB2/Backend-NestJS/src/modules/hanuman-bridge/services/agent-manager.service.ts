import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import { StructuredLoggerService } from '../../logging/structured-logger.service';
import { PrometheusService } from '../../monitoring/prometheus.service';

interface Agent {
  agentId: string;
  agentType: string;
  capabilities: string[];
  version: string;
  status: 'connected' | 'disconnected' | 'busy' | 'idle' | 'error';
  lastSeen: Date;
  socketId?: string;
  metrics?: any;
  configuration?: any;
}

interface AnalysisReport {
  reportId: string;
  agentId: string;
  analysisType: string;
  findings: any[];
  recommendations: string[];
  severity: 'low' | 'medium' | 'high' | 'critical';
  metadata?: any;
  timestamp: Date;
}

/**
 * Service de gestion des agents Hanuman
 */
@Injectable()
export class AgentManagerService {
  private readonly logger = new Logger(AgentManagerService.name);
  private agents = new Map<string, Agent>();
  private analysisReports = new Map<string, AnalysisReport>();
  private agentConfigurations = new Map<string, any>();

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly structuredLogger: StructuredLoggerService,
    private readonly prometheusService: PrometheusService,
  ) {
    this.initializeDefaultConfigurations();
  }

  /**
   * Valider un agent
   */
  async validateAgent(agentId: string, agentType: string, apiKey?: string): Promise<boolean> {
    try {
      // Vérifier l'API key si fournie
      if (apiKey) {
        const expectedApiKey = this.configService.get(`HANUMAN_${agentType.toUpperCase()}_API_KEY`);
        if (expectedApiKey && apiKey !== expectedApiKey) {
          this.logger.warn(`Invalid API key for agent ${agentId}`);
          return false;
        }
      }

      // Vérifier que le type d'agent est supporté
      const supportedTypes = ['security', 'performance', 'qa', 'devops', 'analytics'];
      if (!supportedTypes.includes(agentType)) {
        this.logger.warn(`Unsupported agent type: ${agentType}`);
        return false;
      }

      // Vérifier les limites de connexion
      const maxAgentsPerType = this.configService.get('HANUMAN_MAX_AGENTS_PER_TYPE', 5);
      const currentAgentsOfType = Array.from(this.agents.values())
        .filter(agent => agent.agentType === agentType && agent.status === 'connected').length;

      if (currentAgentsOfType >= maxAgentsPerType) {
        this.logger.warn(`Maximum agents of type ${agentType} reached`);
        return false;
      }

      return true;
    } catch (error) {
      this.logger.error(`Agent validation failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Enregistrer un agent
   */
  async registerAgent(agentData: Partial<Agent>): Promise<void> {
    try {
      const agent: Agent = {
        agentId: agentData.agentId,
        agentType: agentData.agentType,
        capabilities: agentData.capabilities || [],
        version: agentData.version || '1.0.0',
        status: 'connected',
        lastSeen: new Date(),
        socketId: agentData.socketId,
        configuration: this.agentConfigurations.get(agentData.agentType),
      };

      this.agents.set(agent.agentId, agent);

      // Log et métriques
      this.structuredLogger.logBusinessEvent(
        'agent_registered',
        'hanuman_agent',
        agent.agentId,
        'register',
        'success',
        { agentType: agent.agentType, capabilities: agent.capabilities }
      );

      this.prometheusService.recordHanumanEvent('agent_registered', {
        agent_type: agent.agentType,
      });

      this.logger.log(`Agent registered: ${agent.agentId} (${agent.agentType})`);
    } catch (error) {
      this.logger.error(`Agent registration failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Mettre à jour le statut d'un agent
   */
  async updateAgentStatus(agentId: string, status: string, metrics?: any): Promise<void> {
    const agent = this.agents.get(agentId);
    if (!agent) {
      this.logger.warn(`Agent not found: ${agentId}`);
      return;
    }

    agent.status = status as any;
    agent.lastSeen = new Date();
    if (metrics) {
      agent.metrics = metrics;
    }

    // Émettre événement de mise à jour
    this.eventEmitter.emit('hanuman.agent.status.updated', {
      agentId,
      status,
      metrics,
      timestamp: new Date(),
    });

    // Métriques Prometheus
    this.prometheusService.recordHanumanEvent('agent_status_updated', {
      agent_type: agent.agentType,
      status,
    });
  }

  /**
   * Gérer la déconnexion d'un agent
   */
  async handleAgentDisconnection(agentId: string): Promise<void> {
    const agent = this.agents.get(agentId);
    if (!agent) return;

    agent.status = 'disconnected';
    agent.lastSeen = new Date();
    agent.socketId = undefined;

    // Nettoyer les tâches en cours
    this.eventEmitter.emit('hanuman.agent.cleanup.required', {
      agentId,
      agentType: agent.agentType,
      timestamp: new Date(),
    });

    this.logger.log(`Agent disconnected: ${agentId}`);
  }

  /**
   * Obtenir la configuration d'un type d'agent
   */
  async getAgentConfiguration(agentType: string): Promise<any> {
    return this.agentConfigurations.get(agentType) || {};
  }

  /**
   * Stocker un rapport d'analyse
   */
  async storeAnalysisReport(report: AnalysisReport): Promise<void> {
    this.analysisReports.set(report.reportId, report);

    // Log structuré
    this.structuredLogger.logBusinessEvent(
      'analysis_report_received',
      'hanuman_analysis',
      report.reportId,
      'store',
      'success',
      {
        agentId: report.agentId,
        analysisType: report.analysisType,
        severity: report.severity,
        findingsCount: report.findings.length,
      }
    );

    // Métriques
    this.prometheusService.recordHanumanEvent('analysis_report_stored', {
      agent_type: this.agents.get(report.agentId)?.agentType || 'unknown',
      analysis_type: report.analysisType,
      severity: report.severity,
    });

    this.logger.log(`Analysis report stored: ${report.reportId} from ${report.agentId}`);
  }

  /**
   * Obtenir tous les agents
   */
  getAllAgents(): Agent[] {
    return Array.from(this.agents.values());
  }

  /**
   * Obtenir les agents par type
   */
  getAgentsByType(agentType: string): Agent[] {
    return Array.from(this.agents.values()).filter(agent => agent.agentType === agentType);
  }

  /**
   * Obtenir les agents connectés
   */
  getConnectedAgents(): Agent[] {
    return Array.from(this.agents.values()).filter(agent => agent.status === 'connected');
  }

  /**
   * Obtenir les statistiques des agents
   */
  getAgentStatistics() {
    const allAgents = this.getAllAgents();
    const connectedAgents = this.getConnectedAgents();

    const byType = allAgents.reduce((acc, agent) => {
      acc[agent.agentType] = (acc[agent.agentType] || 0) + 1;
      return acc;
    }, {});

    const byStatus = allAgents.reduce((acc, agent) => {
      acc[agent.status] = (acc[agent.status] || 0) + 1;
      return acc;
    }, {});

    return {
      total: allAgents.length,
      connected: connectedAgents.length,
      byType,
      byStatus,
      lastUpdate: new Date(),
    };
  }

  /**
   * Obtenir les rapports d'analyse récents
   */
  getRecentAnalysisReports(limit: number = 10): AnalysisReport[] {
    return Array.from(this.analysisReports.values())
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  /**
   * Obtenir les rapports par sévérité
   */
  getReportsBySeverity(severity: string): AnalysisReport[] {
    return Array.from(this.analysisReports.values())
      .filter(report => report.severity === severity);
  }

  /**
   * Tâches de maintenance automatique
   */
  @Cron(CronExpression.EVERY_MINUTE)
  async performHealthCheck() {
    const now = new Date();
    const timeoutThreshold = 2 * 60 * 1000; // 2 minutes

    for (const [agentId, agent] of this.agents.entries()) {
      if (agent.status === 'connected') {
        const timeSinceLastSeen = now.getTime() - agent.lastSeen.getTime();
        
        if (timeSinceLastSeen > timeoutThreshold) {
          this.logger.warn(`Agent ${agentId} appears to be unresponsive`);
          
          // Marquer comme déconnecté
          agent.status = 'disconnected';
          
          // Émettre événement
          this.eventEmitter.emit('hanuman.agent.timeout', {
            agentId,
            agentType: agent.agentType,
            lastSeen: agent.lastSeen,
            timestamp: now,
          });
        }
      }
    }
  }

  @Cron(CronExpression.EVERY_HOUR)
  async performMaintenanceTasks() {
    // Nettoyer les anciens rapports (garder 24h)
    const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000);
    
    for (const [reportId, report] of this.analysisReports.entries()) {
      if (report.timestamp < cutoffTime) {
        this.analysisReports.delete(reportId);
      }
    }

    // Nettoyer les agents déconnectés depuis plus de 1 heure
    const agentCutoffTime = new Date(Date.now() - 60 * 60 * 1000);
    
    for (const [agentId, agent] of this.agents.entries()) {
      if (agent.status === 'disconnected' && agent.lastSeen < agentCutoffTime) {
        this.agents.delete(agentId);
        this.logger.log(`Cleaned up old agent: ${agentId}`);
      }
    }

    this.logger.log('Maintenance tasks completed');
  }

  /**
   * Initialiser les configurations par défaut
   */
  private initializeDefaultConfigurations() {
    // Configuration pour agent de sécurité
    this.agentConfigurations.set('security', {
      scanInterval: 300000, // 5 minutes
      alertThresholds: {
        critical: 1,
        high: 5,
        medium: 10,
      },
      enabledScans: ['vulnerability', 'compliance', 'intrusion'],
      reportFormat: 'detailed',
    });

    // Configuration pour agent de performance
    this.agentConfigurations.set('performance', {
      monitoringInterval: 60000, // 1 minute
      thresholds: {
        cpu: 80,
        memory: 85,
        disk: 90,
        responseTime: 1000,
      },
      enabledMetrics: ['system', 'application', 'database'],
      optimizationMode: 'automatic',
    });

    // Configuration pour agent QA
    this.agentConfigurations.set('qa', {
      testInterval: 1800000, // 30 minutes
      testSuites: ['unit', 'integration', 'e2e'],
      reportLevel: 'detailed',
      autoFix: false,
    });

    // Configuration pour agent DevOps
    this.agentConfigurations.set('devops', {
      deploymentMode: 'blue-green',
      rollbackThreshold: 5, // % d'erreurs
      monitoringDuration: 600000, // 10 minutes
      enabledChecks: ['health', 'performance', 'security'],
    });

    // Configuration pour agent Analytics
    this.agentConfigurations.set('analytics', {
      analysisInterval: 3600000, // 1 heure
      dataRetention: 30, // jours
      enabledAnalytics: ['user_behavior', 'business_metrics', 'predictions'],
      mlModels: ['conversion', 'churn', 'recommendation'],
    });

    this.logger.log('Default agent configurations initialized');
  }
}
