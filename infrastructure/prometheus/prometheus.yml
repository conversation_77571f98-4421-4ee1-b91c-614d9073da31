global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'retreat-and-be'
    environment: 'production'

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus lui-même
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s

  # Application Phase 2
  - job_name: 'retreat-phase2'
    static_configs:
      - targets: ['host.docker.internal:3001']
    scrape_interval: 15s
    metrics_path: '/metrics'
    scrape_timeout: 10s

  # Backend NestJS (quand disponible)
  - job_name: 'retreat-backend'
    static_configs:
      - targets: ['host.docker.internal:3000']
    scrape_interval: 15s
    metrics_path: '/metrics'
    scrape_timeout: 10s

  # Node Exporter - Métriques système
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # cAdvisor - Métriques containers
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s

  # Redis Exporter
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s

  # Grafana
  - job_name: 'grafana'
    static_configs:
      - targets: ['grafana:3000']
    scrape_interval: 60s

  # AlertManager
  - job_name: 'alertmanager'
    static_configs:
      - targets: ['alertmanager:9093']
    scrape_interval: 60s
