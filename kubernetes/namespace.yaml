apiVersion: v1
kind: Namespace
metadata:
  name: retreat-and-be
  labels:
    name: retreat-and-be
    environment: production
    project: phase3-scalability
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: retreat-quota
  namespace: retreat-and-be
spec:
  hard:
    requests.cpu: "4"
    requests.memory: 8Gi
    limits.cpu: "8"
    limits.memory: 16Gi
    persistentvolumeclaims: "10"
    services: "10"
    secrets: "10"
    configmaps: "10"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: retreat-limits
  namespace: retreat-and-be
spec:
  limits:
  - default:
      cpu: 500m
      memory: 512Mi
    defaultRequest:
      cpu: 100m
      memory: 128Mi
    type: Container
