apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: retreat-ingress
  namespace: retreat-and-be
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,X-Correlation-ID"
spec:
  tls:
  - hosts:
    - api.retreat-and-be.com
    - hanuman.retreat-and-be.com
    - analytics.retreat-and-be.com
    secretName: retreat-tls-secret
  rules:
  - host: api.retreat-and-be.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: retreat-backend-service
            port:
              number: 3000
      - path: /metrics
        pathType: Prefix
        backend:
          service:
            name: retreat-backend-service
            port:
              number: 9464
  - host: hanuman.retreat-and-be.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: hanuman-bridge-service
            port:
              number: 3001
      - path: /ws
        pathType: Prefix
        backend:
          service:
            name: hanuman-bridge-service
            port:
              number: 3002
  - host: analytics.retreat-and-be.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: analytics-ml-service
            port:
              number: 3003
---
apiVersion: v1
kind: Service
metadata:
  name: retreat-frontend-service
  namespace: retreat-and-be
  labels:
    app: retreat-frontend
spec:
  type: LoadBalancer
  ports:
  - port: 80
    targetPort: 3000
    protocol: TCP
    name: http
  - port: 443
    targetPort: 3000
    protocol: TCP
    name: https
  selector:
    app: retreat-frontend
  loadBalancerSourceRanges:
  - 0.0.0.0/0
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: retreat-network-policy
  namespace: retreat-and-be
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    - namespaceSelector:
        matchLabels:
          name: monitoring
    - podSelector:
        matchLabels:
          app: retreat-backend
    - podSelector:
        matchLabels:
          app: hanuman-bridge
    - podSelector:
        matchLabels:
          app: analytics-ml
    ports:
    - protocol: TCP
      port: 3000
    - protocol: TCP
      port: 3001
    - protocol: TCP
      port: 3002
    - protocol: TCP
      port: 3003
    - protocol: TCP
      port: 9464
    - protocol: TCP
      port: 9465
    - protocol: TCP
      port: 9466
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 5432  # PostgreSQL
    - protocol: TCP
      port: 6379  # Redis
    - protocol: TCP
      port: 5672  # RabbitMQ
