apiVersion: apps/v1
kind: Deployment
metadata:
  name: analytics-ml
  namespace: retreat-and-be
  labels:
    app: analytics-ml
    tier: analytics
    component: machine-learning
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: analytics-ml
  template:
    metadata:
      labels:
        app: analytics-ml
        tier: analytics
        component: machine-learning
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3003"
        prometheus.io/path: "/analytics/metrics"
    spec:
      containers:
      - name: analytics-ml
        image: analytics-ml:3.0.0
        ports:
        - containerPort: 3003
          name: http
        - containerPort: 9466
          name: metrics
        env:
        - name: NODE_ENV
          value: "production"
        - name: ANALYTICS_PORT
          value: "3003"
        - name: TENSORFLOW_BACKEND
          value: "cpu"
        - name: ML_MODEL_PATH
          value: "/app/models"
        - name: TRAINING_DATA_PATH
          value: "/app/data"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: retreat-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: retreat-secrets
              key: redis-url
        - name: ML_TRAINING_SCHEDULE
          value: "0 2 * * *"  # Daily at 2 AM
        - name: PREDICTION_CACHE_TTL
          value: "3600"  # 1 hour
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 2000m
            memory: 4Gi
        livenessProbe:
          httpGet:
            path: /analytics/health
            port: 3003
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
        readinessProbe:
          httpGet:
            path: /analytics/ready
            port: 3003
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
        volumeMounts:
        - name: ml-models
          mountPath: /app/models
        - name: training-data
          mountPath: /app/data
        - name: ml-cache
          mountPath: /app/cache
      volumes:
      - name: ml-models
        persistentVolumeClaim:
          claimName: ml-models-pvc
      - name: training-data
        persistentVolumeClaim:
          claimName: training-data-pvc
      - name: ml-cache
        emptyDir:
          sizeLimit: 1Gi
---
apiVersion: v1
kind: Service
metadata:
  name: analytics-ml-service
  namespace: retreat-and-be
  labels:
    app: analytics-ml
spec:
  type: ClusterIP
  ports:
  - port: 3003
    targetPort: 3003
    protocol: TCP
    name: http
  - port: 9466
    targetPort: 9466
    protocol: TCP
    name: metrics
  selector:
    app: analytics-ml
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: ml-models-pvc
  namespace: retreat-and-be
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: fast-ssd
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: training-data-pvc
  namespace: retreat-and-be
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 50Gi
  storageClassName: standard
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: analytics-ml-hpa
  namespace: retreat-and-be
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: analytics-ml
  minReplicas: 1
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 75
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 85
  - type: Pods
    pods:
      metric:
        name: ml_predictions_per_second
      target:
        type: AverageValue
        averageValue: "10"
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 600  # 10 minutes
      policies:
      - type: Percent
        value: 25
        periodSeconds: 120
    scaleUp:
      stabilizationWindowSeconds: 120  # 2 minutes
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
