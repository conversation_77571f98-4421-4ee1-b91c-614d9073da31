const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const winston = require('winston');
const client = require('prom-client');
const { v4: uuidv4 } = require('uuid');

const app = express();
const PORT = process.env.PORT || 3001;

// Configuration Winston pour logging structuré
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

// Configuration Prometheus
const register = new client.Registry();
client.collectDefaultMetrics({ register });

// Métriques custom
const httpRequestsTotal = new client.Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code'],
  registers: [register]
});

const httpRequestDuration = new client.Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route'],
  registers: [register]
});

const businessMetrics = {
  userRegistrations: new client.Counter({
    name: 'user_registrations_total',
    help: 'Total user registrations',
    labelNames: ['source'],
    registers: [register]
  }),
  
  systemHealth: new client.Gauge({
    name: 'system_health_score',
    help: 'System health score (0-100)',
    registers: [register]
  })
};

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json());

// Middleware de correlation ID
app.use((req, res, next) => {
  const correlationId = req.headers['x-correlation-id'] || uuidv4();
  const requestId = req.headers['x-request-id'] || uuidv4();
  
  req.correlationId = correlationId;
  req.requestId = requestId;
  req.startTime = Date.now();
  
  res.setHeader('X-Correlation-ID', correlationId);
  res.setHeader('X-Request-ID', requestId);
  
  // Log structuré
  logger.info('HTTP Request started', {
    correlationId,
    requestId,
    method: req.method,
    url: req.url,
    userAgent: req.headers['user-agent'],
    ip: req.ip
  });
  
  next();
});

// Middleware de métriques
app.use((req, res, next) => {
  const startTime = Date.now();
  
  res.on('finish', () => {
    const duration = (Date.now() - startTime) / 1000;
    const route = req.route ? req.route.path : req.path;
    
    httpRequestsTotal.inc({
      method: req.method,
      route,
      status_code: res.statusCode
    });
    
    httpRequestDuration.observe({
      method: req.method,
      route
    }, duration);
    
    // Log de fin de requête
    logger.info('HTTP Request completed', {
      correlationId: req.correlationId,
      requestId: req.requestId,
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration: Date.now() - req.startTime
    });
  });
  
  next();
});

// Routes Phase 2

// Métriques Prometheus
app.get('/metrics', async (req, res) => {
  res.set('Content-Type', register.contentType);
  res.end(await register.metrics());
});

// Health Checks
app.get('/health', (req, res) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    services: {
      prometheus: { status: 'healthy' },
      logging: { status: 'healthy' },
      validation: { status: 'healthy' }
    },
    circuitBreakers: {
      'test-service': 'CLOSED'
    }
  };
  
  businessMetrics.systemHealth.set(100);
  
  res.json(health);
});

app.get('/health/live', (req, res) => {
  res.json({
    status: 'alive',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

app.get('/health/ready', (req, res) => {
  res.json({
    status: 'ready',
    ready: true,
    timestamp: new Date().toISOString()
  });
});

// Business Metrics
app.get('/metrics/business', (req, res) => {
  res.json({
    timestamp: new Date().toISOString(),
    metrics: {
      userRegistrations: 'Available in /metrics endpoint',
      systemHealth: 'Available in /metrics endpoint'
    },
    note: 'Use /metrics endpoint for Prometheus scraping'
  });
});

// Test de validation
app.post('/api/test-validation', (req, res) => {
  const { name, email, data } = req.body;
  
  // Simulation de validation
  const errors = [];
  
  if (!name || name.length < 2) {
    errors.push('Name must be at least 2 characters');
  }
  
  if (name && /<script|javascript:|on\w+=/i.test(name)) {
    logger.warn('XSS attempt detected', {
      correlationId: req.correlationId,
      input: name,
      type: 'xss_attempt'
    });
    errors.push('Invalid characters detected');
  }
  
  if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
    errors.push('Invalid email format');
  }
  
  if (errors.length > 0) {
    return res.status(400).json({
      message: 'Validation failed',
      errors,
      statusCode: 400
    });
  }
  
  // Log événement business
  logger.info('Validation successful', {
    correlationId: req.correlationId,
    business: {
      event: 'validation_success',
      entity: 'test_data',
      action: 'validate',
      result: 'success'
    }
  });
  
  businessMetrics.userRegistrations.inc({ source: 'test' });
  
  res.json({
    message: 'Validation successful',
    data: { name, email },
    sanitized: true
  });
});

// Test de tracing
app.get('/api/test-tracing', (req, res) => {
  const traceId = uuidv4();
  
  logger.info('Tracing test started', {
    correlationId: req.correlationId,
    traceId,
    operation: 'test_tracing'
  });
  
  // Simulation d'opération avec tracing
  setTimeout(() => {
    logger.info('Tracing test completed', {
      correlationId: req.correlationId,
      traceId,
      operation: 'test_tracing',
      duration: 100
    });
    
    res.json({
      message: 'Tracing test completed',
      traceId,
      correlationId: req.correlationId
    });
  }, 100);
});

// Gestion d'erreur globale
app.use((err, req, res, next) => {
  logger.error('Unhandled error', {
    correlationId: req.correlationId,
    error: {
      name: err.name,
      message: err.message,
      stack: err.stack
    }
  });
  
  res.status(500).json({
    message: 'Internal server error',
    correlationId: req.correlationId
  });
});

// Démarrage du serveur
app.listen(PORT, () => {
  logger.info(`Phase 2 Test Server started on port ${PORT}`, {
    port: PORT,
    environment: 'test',
    features: ['prometheus', 'structured-logging', 'validation', 'tracing']
  });
  
  console.log(`
🚀 Phase 2 Test Server Running!

📊 Endpoints disponibles:
  Health Checks:
    GET  http://localhost:${PORT}/health
    GET  http://localhost:${PORT}/health/live  
    GET  http://localhost:${PORT}/health/ready

  Métriques:
    GET  http://localhost:${PORT}/metrics
    GET  http://localhost:${PORT}/metrics/business

  Tests:
    POST http://localhost:${PORT}/api/test-validation
    GET  http://localhost:${PORT}/api/test-tracing

🎯 Fonctionnalités Phase 2:
  ✅ Logging structuré avec Winston
  ✅ Métriques Prometheus
  ✅ Correlation IDs
  ✅ Validation avec sanitisation
  ✅ Tracing distribué (simulation)
  ✅ Health checks complets
  `);
});
