import { Controller, Get, Post, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { HanumanBridgeService } from './hanuman-bridge.service';
import { AgentManagerService } from './services/agent-manager.service';
import { TaskOrchestratorService } from './services/task-orchestrator.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';

/**
 * Contrôleur API pour Hanuman Bridge
 */
@ApiTags('Hanuman Bridge')
@Controller('hanuman')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class HanumanBridgeController {
  constructor(
    private readonly hanumanBridge: HanumanBridgeService,
    private readonly agentManager: AgentManagerService,
    private readonly taskOrchestrator: TaskOrchestratorService,
  ) {}

  /**
   * Obtenir le statut du bridge Hanuman
   */
  @Get('status')
  @ApiOperation({ summary: 'Get Hanuman Bridge status' })
  @ApiResponse({ status: 200, description: 'Bridge status retrieved successfully' })
  @Roles('admin', 'operator')
  async getBridgeStatus() {
    const agentStats = this.agentManager.getAgentStatistics();
    const connectedAgents = this.agentManager.getConnectedAgents();
    
    return {
      status: 'operational',
      timestamp: new Date().toISOString(),
      bridge: {
        version: '3.0.0',
        uptime: process.uptime(),
        websocketConnections: connectedAgents.length,
      },
      agents: agentStats,
      tasks: await this.taskOrchestrator.getTaskStatistics(),
      lastUpdate: new Date().toISOString(),
    };
  }

  /**
   * Obtenir la liste des agents connectés
   */
  @Get('agents')
  @ApiOperation({ summary: 'Get connected agents' })
  @ApiResponse({ status: 200, description: 'Agents list retrieved successfully' })
  @Roles('admin', 'operator')
  async getConnectedAgents(@Query('type') agentType?: string) {
    if (agentType) {
      return {
        agents: this.agentManager.getAgentsByType(agentType),
        total: this.agentManager.getAgentsByType(agentType).length,
        type: agentType,
      };
    }

    return {
      agents: this.agentManager.getAllAgents(),
      statistics: this.agentManager.getAgentStatistics(),
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Obtenir les détails d'un agent spécifique
   */
  @Get('agents/:agentId')
  @ApiOperation({ summary: 'Get agent details' })
  @ApiResponse({ status: 200, description: 'Agent details retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  @Roles('admin', 'operator')
  async getAgentDetails(@Param('agentId') agentId: string) {
    const agents = this.agentManager.getAllAgents();
    const agent = agents.find(a => a.agentId === agentId);
    
    if (!agent) {
      return { error: 'Agent not found', agentId };
    }

    const recentTasks = await this.taskOrchestrator.getAgentTasks(agentId, 10);
    
    return {
      agent,
      recentTasks,
      performance: agent.metrics || {},
      lastUpdate: new Date().toISOString(),
    };
  }

  /**
   * Envoyer une tâche à un agent
   */
  @Post('agents/:agentId/tasks')
  @ApiOperation({ summary: 'Assign task to agent' })
  @ApiResponse({ status: 201, description: 'Task assigned successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  @Roles('admin', 'operator')
  async assignTaskToAgent(
    @Param('agentId') agentId: string,
    @Body() taskData: {
      command: string;
      parameters: any;
      priority?: number;
      deadline?: string;
    }
  ) {
    const agents = this.agentManager.getAllAgents();
    const agent = agents.find(a => a.agentId === agentId);
    
    if (!agent) {
      return { error: 'Agent not found', agentId };
    }

    if (agent.status !== 'connected') {
      return { error: 'Agent not connected', agentId, status: agent.status };
    }

    const task = await this.taskOrchestrator.assignTaskToAgent(agentId, {
      command: taskData.command,
      parameters: taskData.parameters,
      priority: taskData.priority || 1,
      deadline: taskData.deadline ? new Date(taskData.deadline) : undefined,
    });

    return {
      success: true,
      task,
      message: `Task assigned to agent ${agentId}`,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Diffuser une tâche à tous les agents d'un type
   */
  @Post('agents/broadcast/:agentType')
  @ApiOperation({ summary: 'Broadcast task to all agents of a type' })
  @ApiResponse({ status: 201, description: 'Task broadcasted successfully' })
  @Roles('admin')
  async broadcastTaskToAgentType(
    @Param('agentType') agentType: string,
    @Body() taskData: {
      command: string;
      parameters: any;
      priority?: number;
    }
  ) {
    const agents = this.agentManager.getAgentsByType(agentType)
      .filter(agent => agent.status === 'connected');

    if (agents.length === 0) {
      return {
        error: 'No connected agents of this type',
        agentType,
        availableTypes: [...new Set(this.agentManager.getConnectedAgents().map(a => a.agentType))],
      };
    }

    const results = await Promise.all(
      agents.map(agent => 
        this.taskOrchestrator.assignTaskToAgent(agent.agentId, {
          command: taskData.command,
          parameters: taskData.parameters,
          priority: taskData.priority || 1,
        })
      )
    );

    return {
      success: true,
      agentType,
      tasksAssigned: results.length,
      agents: agents.map(a => a.agentId),
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Obtenir les tâches en cours
   */
  @Get('tasks')
  @ApiOperation({ summary: 'Get active tasks' })
  @ApiResponse({ status: 200, description: 'Tasks retrieved successfully' })
  @Roles('admin', 'operator')
  async getActiveTasks(
    @Query('status') status?: string,
    @Query('agentType') agentType?: string,
    @Query('limit') limit?: number
  ) {
    const tasks = await this.taskOrchestrator.getTasks({
      status,
      agentType,
      limit: limit || 50,
    });

    return {
      tasks,
      statistics: await this.taskOrchestrator.getTaskStatistics(),
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Obtenir les rapports d'analyse récents
   */
  @Get('analysis/reports')
  @ApiOperation({ summary: 'Get recent analysis reports' })
  @ApiResponse({ status: 200, description: 'Analysis reports retrieved successfully' })
  @Roles('admin', 'operator')
  async getAnalysisReports(
    @Query('severity') severity?: string,
    @Query('limit') limit?: number
  ) {
    let reports;
    
    if (severity) {
      reports = this.agentManager.getReportsBySeverity(severity);
    } else {
      reports = this.agentManager.getRecentAnalysisReports(limit || 20);
    }

    const severityCounts = reports.reduce((acc, report) => {
      acc[report.severity] = (acc[report.severity] || 0) + 1;
      return acc;
    }, {});

    return {
      reports,
      statistics: {
        total: reports.length,
        bySeverity: severityCounts,
        lastReport: reports[0]?.timestamp,
      },
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Obtenir les métriques de performance du bridge
   */
  @Get('metrics')
  @ApiOperation({ summary: 'Get bridge performance metrics' })
  @ApiResponse({ status: 200, description: 'Metrics retrieved successfully' })
  @Roles('admin', 'operator')
  async getBridgeMetrics() {
    const agentStats = this.agentManager.getAgentStatistics();
    const taskStats = await this.taskOrchestrator.getTaskStatistics();
    
    return {
      timestamp: new Date().toISOString(),
      bridge: {
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
        cpuUsage: process.cpuUsage(),
      },
      agents: {
        total: agentStats.total,
        connected: agentStats.connected,
        byType: agentStats.byType,
        byStatus: agentStats.byStatus,
      },
      tasks: taskStats,
      websocket: {
        connections: agentStats.connected,
        rooms: ['security', 'performance', 'qa', 'devops', 'analytics'].length,
      },
    };
  }

  /**
   * Redémarrer un agent
   */
  @Post('agents/:agentId/restart')
  @ApiOperation({ summary: 'Restart an agent' })
  @ApiResponse({ status: 200, description: 'Agent restart initiated' })
  @Roles('admin')
  async restartAgent(@Param('agentId') agentId: string) {
    const success = await this.hanumanBridge.restartAgent(agentId);
    
    return {
      success,
      agentId,
      message: success ? 'Agent restart initiated' : 'Failed to restart agent',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Mettre à jour la configuration d'un agent
   */
  @Post('agents/:agentId/config')
  @ApiOperation({ summary: 'Update agent configuration' })
  @ApiResponse({ status: 200, description: 'Configuration updated successfully' })
  @Roles('admin')
  async updateAgentConfig(
    @Param('agentId') agentId: string,
    @Body() config: any
  ) {
    const success = await this.hanumanBridge.updateAgentConfiguration(agentId, config);
    
    return {
      success,
      agentId,
      config,
      message: success ? 'Configuration updated' : 'Failed to update configuration',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Obtenir les logs d'un agent
   */
  @Get('agents/:agentId/logs')
  @ApiOperation({ summary: 'Get agent logs' })
  @ApiResponse({ status: 200, description: 'Agent logs retrieved successfully' })
  @Roles('admin', 'operator')
  async getAgentLogs(
    @Param('agentId') agentId: string,
    @Query('limit') limit?: number,
    @Query('level') level?: string
  ) {
    const logs = await this.hanumanBridge.getAgentLogs(agentId, {
      limit: limit || 100,
      level,
    });
    
    return {
      agentId,
      logs,
      total: logs.length,
      timestamp: new Date().toISOString(),
    };
  }
}
