groups:
  - name: retreat-and-be-alerts
    rules:
      # Alertes Application
      - alert: ApplicationDown
        expr: up{job="retreat-phase2"} == 0
        for: 1m
        labels:
          severity: critical
          service: retreat-phase2
        annotations:
          summary: "Application Retreat And Be Phase 2 est down"
          description: "L'application Phase 2 n'est pas accessible depuis {{ $labels.instance }}"

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 0.5
        for: 2m
        labels:
          severity: warning
          service: retreat-phase2
        annotations:
          summary: "Temps de réponse élevé détecté"
          description: "95% des requêtes prennent plus de 500ms sur {{ $labels.instance }}"

      - alert: HighErrorRate
        expr: rate(http_requests_total{status_code=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 2m
        labels:
          severity: critical
          service: retreat-phase2
        annotations:
          summary: "Taux d'erreur élevé détecté"
          description: "Plus de 5% d'erreurs 5xx sur {{ $labels.instance }}"

      # Alertes Sécurité
      - alert: XSSAttackDetected
        expr: increase(xss_attempts_total[5m]) > 5
        for: 1m
        labels:
          severity: warning
          service: security
        annotations:
          summary: "Tentatives d'attaque XSS détectées"
          description: "{{ $value }} tentatives XSS détectées en 5 minutes"

      - alert: RateLimitExceeded
        expr: increase(rate_limit_exceeded_total[5m]) > 10
        for: 1m
        labels:
          severity: warning
          service: security
        annotations:
          summary: "Rate limiting dépassé fréquemment"
          description: "{{ $value }} dépassements de rate limit en 5 minutes"

      # Alertes Infrastructure
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.85
        for: 5m
        labels:
          severity: warning
          service: infrastructure
        annotations:
          summary: "Utilisation mémoire élevée"
          description: "Utilisation mémoire > 85% sur {{ $labels.instance }}"

      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          service: infrastructure
        annotations:
          summary: "Utilisation CPU élevée"
          description: "Utilisation CPU > 80% sur {{ $labels.instance }}"

      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) * 100 < 10
        for: 5m
        labels:
          severity: critical
          service: infrastructure
        annotations:
          summary: "Espace disque faible"
          description: "Moins de 10% d'espace libre sur {{ $labels.device }} ({{ $labels.instance }})"

      # Alertes Business
      - alert: LowUserRegistrations
        expr: rate(user_registrations_total[1h]) < 0.1
        for: 30m
        labels:
          severity: warning
          service: business
        annotations:
          summary: "Faible taux d'inscription"
          description: "Moins de 6 inscriptions par heure détectées"

      - alert: SystemHealthDegraded
        expr: system_health_score < 80
        for: 5m
        labels:
          severity: warning
          service: business
        annotations:
          summary: "Score de santé système dégradé"
          description: "Score de santé système: {{ $value }}/100"

      # Alertes Circuit Breakers
      - alert: CircuitBreakerOpen
        expr: circuit_breaker_state == 1
        for: 1m
        labels:
          severity: warning
          service: resilience
        annotations:
          summary: "Circuit breaker ouvert"
          description: "Circuit breaker {{ $labels.service }} est ouvert"

      - alert: HighCircuitBreakerFailures
        expr: rate(circuit_breaker_failures_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          service: resilience
        annotations:
          summary: "Taux d'échec élevé circuit breaker"
          description: "Service {{ $labels.service }} a un taux d'échec élevé"

      # Alertes Cache
      - alert: LowCacheHitRate
        expr: rate(cache_hits_total[5m]) / (rate(cache_hits_total[5m]) + rate(cache_misses_total[5m])) < 0.7
        for: 5m
        labels:
          severity: warning
          service: cache
        annotations:
          summary: "Taux de hit cache faible"
          description: "Taux de hit cache < 70% pour {{ $labels.cache_name }}"

      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
          service: cache
        annotations:
          summary: "Redis est down"
          description: "Redis n'est pas accessible"
