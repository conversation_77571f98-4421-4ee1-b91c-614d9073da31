
> phase2-test-server@1.0.0 start
> node server.js

[32minfo[39m: Phase 2 Test Server started on port 3001 {"environment":"test","features":["prometheus","structured-logging","validation","tracing"],"port":3001,"timestamp":"2025-05-28T08:28:15.746Z"}

🚀 Phase 2 Test Server Running!

📊 Endpoints disponibles:
  Health Checks:
    GET  http://localhost:3001/health
    GET  http://localhost:3001/health/live  
    GET  http://localhost:3001/health/ready

  Métriques:
    GET  http://localhost:3001/metrics
    GET  http://localhost:3001/metrics/business

  Tests:
    POST http://localhost:3001/api/test-validation
    GET  http://localhost:3001/api/test-tracing

🎯 Fonctionnalités Phase 2:
  ✅ Logging structuré avec Winston
  ✅ Métriques Prometheus
  ✅ Correlation IDs
  ✅ Validation avec sanitisation
  ✅ Tracing distribué (simulation)
  ✅ Health checks complets
  
[32minfo[39m: HTTP Request started {"correlationId":"1beeb05b-9e3c-4b2a-9c88-b2bf8ce97b6d","ip":"::1","method":"GET","requestId":"4b0c8860-567e-4b51-9c18-ce5184393059","timestamp":"2025-05-28T08:28:16.967Z","url":"/health","userAgent":"curl/8.7.1"}
[32minfo[39m: HTTP Request completed {"correlationId":"1beeb05b-9e3c-4b2a-9c88-b2bf8ce97b6d","duration":6,"method":"GET","requestId":"4b0c8860-567e-4b51-9c18-ce5184393059","statusCode":200,"timestamp":"2025-05-28T08:28:16.973Z","url":"/health"}
[32minfo[39m: HTTP Request started {"correlationId":"e66517b5-972f-43ec-af2e-8c79c4323743","ip":"::1","method":"GET","requestId":"f10257a7-dce5-49e5-9080-ca1f99362441","timestamp":"2025-05-28T08:28:17.010Z","url":"/health","userAgent":"curl/8.7.1"}
[32minfo[39m: HTTP Request completed {"correlationId":"e66517b5-972f-43ec-af2e-8c79c4323743","duration":2,"method":"GET","requestId":"f10257a7-dce5-49e5-9080-ca1f99362441","statusCode":200,"timestamp":"2025-05-28T08:28:17.012Z","url":"/health"}
[32minfo[39m: HTTP Request started {"correlationId":"daf05802-e5f3-45e4-86c7-c651b7a92e5b","ip":"::1","method":"GET","requestId":"ed843c07-150e-4c30-96b3-9148d4f193f7","timestamp":"2025-05-28T08:28:17.033Z","url":"/metrics","userAgent":"curl/8.7.1"}
[32minfo[39m: HTTP Request completed {"correlationId":"daf05802-e5f3-45e4-86c7-c651b7a92e5b","duration":5,"method":"GET","requestId":"ed843c07-150e-4c30-96b3-9148d4f193f7","statusCode":200,"timestamp":"2025-05-28T08:28:17.038Z","url":"/metrics"}
[32minfo[39m: HTTP Request started {"correlationId":"8cd3db53-5cf1-402a-82d5-92a1a06f6bff","ip":"::1","method":"POST","requestId":"1ad4b690-52ea-49e1-a595-94a1f9d7feaf","timestamp":"2025-05-28T08:28:17.073Z","url":"/api/test-validation","userAgent":"curl/8.7.1"}
[33mwarn[39m: XSS attempt detected {"correlationId":"8cd3db53-5cf1-402a-82d5-92a1a06f6bff","input":"<script>alert(xss)</script>","timestamp":"2025-05-28T08:28:17.074Z","type":"xss_attempt"}
[32minfo[39m: HTTP Request completed {"correlationId":"8cd3db53-5cf1-402a-82d5-92a1a06f6bff","duration":2,"method":"POST","requestId":"1ad4b690-52ea-49e1-a595-94a1f9d7feaf","statusCode":400,"timestamp":"2025-05-28T08:28:17.075Z","url":"/api/test-validation"}
[32minfo[39m: HTTP Request started {"correlationId":"59532396-9092-4d0b-85f6-0cf9abadec62","ip":"::1","method":"GET","requestId":"6d117144-01c7-4685-a181-45139191160f","timestamp":"2025-05-28T08:28:17.107Z","url":"/health","userAgent":"curl/8.7.1"}
[32minfo[39m: HTTP Request completed {"correlationId":"59532396-9092-4d0b-85f6-0cf9abadec62","duration":0,"method":"GET","requestId":"6d117144-01c7-4685-a181-45139191160f","statusCode":200,"timestamp":"2025-05-28T08:28:17.107Z","url":"/health"}
