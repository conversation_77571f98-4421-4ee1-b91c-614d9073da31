#!/bin/bash

# Script de tests complets - Option C: Tests Avancés
# Exécute tous les types de tests pour Phase 2

set -e

echo "🧪 Tests Avancés Complets - Option C"
echo "===================================="

# Couleurs
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# Variables
TARGET_URL="http://localhost:3001"
RESULTS_DIR="test-results/$(date +%Y%m%d_%H%M%S)"
PHASE2_SERVER_PID=""

# Créer le répertoire de résultats
mkdir -p "$RESULTS_DIR"

# Fonction de nettoyage
cleanup() {
    log_info "Nettoyage des processus de test..."
    
    # Arrêter le serveur Phase 2 si démarré par ce script
    if [ ! -z "$PHASE2_SERVER_PID" ]; then
        kill $PHASE2_SERVER_PID 2>/dev/null || true
    fi
    
    # Arrêter les processus de test
    pkill -f "artillery" 2>/dev/null || true
    pkill -f "zap" 2>/dev/null || true
    pkill -f "k6" 2>/dev/null || true
    
    log_success "Nettoyage terminé"
}

trap cleanup EXIT INT TERM

# Vérifier les prérequis
check_prerequisites() {
    log_info "Vérification des prérequis..."
    
    # Vérifier Node.js et npm
    if ! command -v node &> /dev/null; then
        log_error "Node.js n'est pas installé"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm n'est pas installé"
        exit 1
    fi
    
    # Vérifier Python pour les tests de sécurité
    if ! command -v python3 &> /dev/null; then
        log_warning "Python3 non trouvé - tests de sécurité désactivés"
        SKIP_SECURITY=true
    fi
    
    # Vérifier Artillery pour les tests de charge
    if ! command -v artillery &> /dev/null; then
        log_info "Installation d'Artillery..."
        npm install -g artillery
    fi
    
    log_success "Prérequis vérifiés"
}

# Démarrer le serveur Phase 2 si nécessaire
ensure_server_running() {
    log_info "Vérification du serveur Phase 2..."
    
    if curl -s "$TARGET_URL/health" > /dev/null; then
        log_success "Serveur Phase 2 déjà en cours d'exécution"
        return
    fi
    
    log_info "Démarrage du serveur Phase 2..."
    
    if [ -d "test-phase2-server" ]; then
        cd test-phase2-server
        npm start &
        PHASE2_SERVER_PID=$!
        cd ..
        
        # Attendre que le serveur soit prêt
        timeout=30
        while [ $timeout -gt 0 ]; do
            if curl -s "$TARGET_URL/health" > /dev/null; then
                log_success "Serveur Phase 2 prêt"
                return
            fi
            sleep 2
            ((timeout-=2))
        done
        
        log_error "Le serveur Phase 2 n'a pas pu démarrer"
        exit 1
    else
        log_error "Répertoire test-phase2-server non trouvé"
        exit 1
    fi
}

# Tests unitaires et d'intégration
run_unit_tests() {
    log_info "Exécution des tests unitaires..."
    
    if [ -f "package.json" ]; then
        npm test > "$RESULTS_DIR/unit-tests.log" 2>&1 || true
        log_success "Tests unitaires terminés"
    else
        log_warning "Pas de tests unitaires configurés"
    fi
}

# Tests de performance avec Artillery
run_performance_tests() {
    log_info "Exécution des tests de performance avec Artillery..."
    
    if [ ! -f "tests/load-testing/artillery-config.yml" ]; then
        log_warning "Configuration Artillery non trouvée - création d'un test simple"
        mkdir -p tests/load-testing
        cat > tests/load-testing/simple-load-test.yml << 'EOF'
config:
  target: 'http://localhost:3001'
  phases:
    - duration: 60
      arrivalRate: 5
scenarios:
  - name: "Simple load test"
    flow:
      - get:
          url: "/health"
      - get:
          url: "/metrics"
EOF
        artillery run tests/load-testing/simple-load-test.yml \
            --output "$RESULTS_DIR/artillery-results.json" > "$RESULTS_DIR/artillery.log" 2>&1
    else
        artillery run tests/load-testing/artillery-config.yml \
            --output "$RESULTS_DIR/artillery-results.json" > "$RESULTS_DIR/artillery.log" 2>&1
    fi
    
    # Générer le rapport HTML
    if [ -f "$RESULTS_DIR/artillery-results.json" ]; then
        artillery report "$RESULTS_DIR/artillery-results.json" \
            --output "$RESULTS_DIR/artillery-report.html"
        log_success "Tests de performance terminés - Rapport: $RESULTS_DIR/artillery-report.html"
    else
        log_warning "Résultats Artillery non générés"
    fi
}

# Tests de sécurité
run_security_tests() {
    if [ "$SKIP_SECURITY" = true ]; then
        log_warning "Tests de sécurité ignorés (Python3 non disponible)"
        return
    fi
    
    log_info "Exécution des tests de sécurité..."
    
    # Tests de sécurité basiques sans ZAP
    run_basic_security_tests
    
    # Tests ZAP si disponible
    if command -v zaproxy &> /dev/null; then
        run_zap_security_tests
    else
        log_warning "OWASP ZAP non trouvé - tests de sécurité basiques uniquement"
    fi
}

# Tests de sécurité basiques
run_basic_security_tests() {
    log_info "Tests de sécurité basiques..."
    
    # Test XSS
    log_info "Test protection XSS..."
    xss_result=$(curl -s -X POST "$TARGET_URL/api/test-validation" \
        -H "Content-Type: application/json" \
        -d '{"name":"<script>alert(\"xss\")</script>","email":"<EMAIL>"}' \
        -w "%{http_code}")
    
    if echo "$xss_result" | grep -q "400"; then
        log_success "Protection XSS: ✅"
        echo "XSS Protection: PASS" >> "$RESULTS_DIR/security-basic.log"
    else
        log_error "Protection XSS: ❌"
        echo "XSS Protection: FAIL" >> "$RESULTS_DIR/security-basic.log"
    fi
    
    # Test injection SQL
    log_info "Test protection SQL injection..."
    sql_result=$(curl -s -X POST "$TARGET_URL/api/test-validation" \
        -H "Content-Type: application/json" \
        -d '{"name":"'; DROP TABLE users; --","email":"<EMAIL>"}' \
        -w "%{http_code}")
    
    if echo "$sql_result" | grep -q "400"; then
        log_success "Protection SQL injection: ✅"
        echo "SQL Injection Protection: PASS" >> "$RESULTS_DIR/security-basic.log"
    else
        log_error "Protection SQL injection: ❌"
        echo "SQL Injection Protection: FAIL" >> "$RESULTS_DIR/security-basic.log"
    fi
    
    # Test headers de sécurité
    log_info "Test headers de sécurité..."
    headers=$(curl -s -I "$TARGET_URL/health")
    
    if echo "$headers" | grep -q "X-Correlation-ID"; then
        log_success "Headers de sécurité: ✅"
        echo "Security Headers: PASS" >> "$RESULTS_DIR/security-basic.log"
    else
        log_warning "Headers de sécurité: ⚠️"
        echo "Security Headers: PARTIAL" >> "$RESULTS_DIR/security-basic.log"
    fi
}

# Tests ZAP
run_zap_security_tests() {
    log_info "Tests de sécurité avec OWASP ZAP..."
    
    if [ -f "tests/security-testing/zap-security-scan.py" ]; then
        python3 tests/security-testing/zap-security-scan.py \
            --target "$TARGET_URL" \
            --quick > "$RESULTS_DIR/zap-security.log" 2>&1 || true
        log_success "Tests ZAP terminés"
    else
        log_warning "Script ZAP non trouvé"
    fi
}

# Tests de monitoring
run_monitoring_tests() {
    log_info "Tests de monitoring et métriques..."
    
    # Test métriques Prometheus
    log_info "Test métriques Prometheus..."
    if curl -s "$TARGET_URL/metrics" | grep -q "http_requests_total"; then
        log_success "Métriques Prometheus: ✅"
        echo "Prometheus Metrics: PASS" >> "$RESULTS_DIR/monitoring.log"
    else
        log_error "Métriques Prometheus: ❌"
        echo "Prometheus Metrics: FAIL" >> "$RESULTS_DIR/monitoring.log"
    fi
    
    # Test health checks
    log_info "Test health checks..."
    health_status=$(curl -s "$TARGET_URL/health" | jq -r '.status' 2>/dev/null || echo "error")
    
    if [ "$health_status" = "healthy" ]; then
        log_success "Health checks: ✅"
        echo "Health Checks: PASS" >> "$RESULTS_DIR/monitoring.log"
    else
        log_error "Health checks: ❌"
        echo "Health Checks: FAIL" >> "$RESULTS_DIR/monitoring.log"
    fi
    
    # Test correlation IDs
    log_info "Test correlation IDs..."
    correlation_test=$(curl -s -H "X-Correlation-ID: test-123" "$TARGET_URL/health/live" \
        -I | grep "X-Correlation-ID: test-123")
    
    if [ ! -z "$correlation_test" ]; then
        log_success "Correlation IDs: ✅"
        echo "Correlation IDs: PASS" >> "$RESULTS_DIR/monitoring.log"
    else
        log_error "Correlation IDs: ❌"
        echo "Correlation IDs: FAIL" >> "$RESULTS_DIR/monitoring.log"
    fi
}

# Tests de validation
run_validation_tests() {
    log_info "Tests de validation avancés..."
    
    # Test validation email
    log_info "Test validation email..."
    email_test=$(curl -s -X POST "$TARGET_URL/api/test-validation" \
        -H "Content-Type: application/json" \
        -d '{"name":"Test","email":"invalid-email"}' \
        -w "%{http_code}")
    
    if echo "$email_test" | grep -q "400"; then
        log_success "Validation email: ✅"
        echo "Email Validation: PASS" >> "$RESULTS_DIR/validation.log"
    else
        log_error "Validation email: ❌"
        echo "Email Validation: FAIL" >> "$RESULTS_DIR/validation.log"
    fi
    
    # Test validation réussie
    log_info "Test validation réussie..."
    success_test=$(curl -s -X POST "$TARGET_URL/api/test-validation" \
        -H "Content-Type: application/json" \
        -d '{"name":"Test User","email":"<EMAIL>"}' \
        -w "%{http_code}")
    
    if echo "$success_test" | grep -q "200"; then
        log_success "Validation réussie: ✅"
        echo "Successful Validation: PASS" >> "$RESULTS_DIR/validation.log"
    else
        log_error "Validation réussie: ❌"
        echo "Successful Validation: FAIL" >> "$RESULTS_DIR/validation.log"
    fi
}

# Générer le rapport final
generate_final_report() {
    log_info "Génération du rapport final..."
    
    cat > "$RESULTS_DIR/test-summary.md" << EOF
# Rapport de Tests Complets - Phase 2 Gap Analysis

## Informations Générales
- **Date**: $(date)
- **Target**: $TARGET_URL
- **Répertoire**: $RESULTS_DIR

## Résultats des Tests

### Tests Unitaires
$([ -f "$RESULTS_DIR/unit-tests.log" ] && echo "✅ Exécutés" || echo "⚠️ Non configurés")

### Tests de Performance
$([ -f "$RESULTS_DIR/artillery-results.json" ] && echo "✅ Exécutés - Voir artillery-report.html" || echo "❌ Échec")

### Tests de Sécurité
$([ -f "$RESULTS_DIR/security-basic.log" ] && echo "✅ Tests basiques exécutés" || echo "❌ Échec")
$([ -f "$RESULTS_DIR/zap-security.log" ] && echo "✅ Tests ZAP exécutés" || echo "⚠️ ZAP non disponible")

### Tests de Monitoring
$([ -f "$RESULTS_DIR/monitoring.log" ] && echo "✅ Exécutés" || echo "❌ Échec")

### Tests de Validation
$([ -f "$RESULTS_DIR/validation.log" ] && echo "✅ Exécutés" || echo "❌ Échec")

## Détails des Résultats

### Sécurité Basique
$([ -f "$RESULTS_DIR/security-basic.log" ] && cat "$RESULTS_DIR/security-basic.log" || echo "Non disponible")

### Monitoring
$([ -f "$RESULTS_DIR/monitoring.log" ] && cat "$RESULTS_DIR/monitoring.log" || echo "Non disponible")

### Validation
$([ -f "$RESULTS_DIR/validation.log" ] && cat "$RESULTS_DIR/validation.log" || echo "Non disponible")

## Recommandations

1. **Performance**: Vérifier les métriques dans artillery-report.html
2. **Sécurité**: Examiner les logs de sécurité pour les vulnérabilités
3. **Monitoring**: S'assurer que tous les tests de monitoring passent
4. **Validation**: Confirmer que la validation fonctionne correctement

## Fichiers Générés

- \`unit-tests.log\`: Logs des tests unitaires
- \`artillery-results.json\`: Résultats bruts Artillery
- \`artillery-report.html\`: Rapport HTML de performance
- \`security-basic.log\`: Résultats tests de sécurité basiques
- \`zap-security.log\`: Logs OWASP ZAP (si disponible)
- \`monitoring.log\`: Résultats tests de monitoring
- \`validation.log\`: Résultats tests de validation

EOF

    log_success "Rapport final généré: $RESULTS_DIR/test-summary.md"
}

# Afficher les résultats
show_results() {
    echo ""
    log_success "🎉 Tests complets terminés !"
    echo ""
    log_info "📊 Résultats disponibles dans: $RESULTS_DIR"
    echo ""
    log_info "📋 Fichiers principaux:"
    echo "  • test-summary.md - Rapport de synthèse"
    echo "  • artillery-report.html - Rapport de performance"
    echo "  • *.log - Logs détaillés par type de test"
    echo ""
    log_info "🔍 Pour voir le rapport de synthèse:"
    echo "  cat $RESULTS_DIR/test-summary.md"
    echo ""
    log_info "🌐 Pour voir le rapport de performance:"
    echo "  open $RESULTS_DIR/artillery-report.html"
}

# Fonction principale
main() {
    check_prerequisites
    ensure_server_running
    
    log_info "Démarrage des tests complets..."
    
    # Exécuter tous les types de tests
    run_unit_tests
    run_performance_tests
    run_security_tests
    run_monitoring_tests
    run_validation_tests
    
    # Générer le rapport final
    generate_final_report
    
    # Afficher les résultats
    show_results
}

# Exécuter
main "$@"
