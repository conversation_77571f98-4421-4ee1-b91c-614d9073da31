import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  MessageBody,
  ConnectedSocket,
} from '@nestjs/websockets';
import { Logger, UseGuards } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { AgentManagerService } from './services/agent-manager.service';
import { TaskOrchestratorService } from './services/task-orchestrator.service';
import { StructuredLoggerService } from '../logging/structured-logger.service';
import { PrometheusService } from '../monitoring/prometheus.service';

interface AgentSocket extends Socket {
  agentId?: string;
  agentType?: string;
  capabilities?: string[];
  lastHeartbeat?: Date;
}

/**
 * Gateway WebSocket pour communication temps réel avec les agents Hanuman
 */
@WebSocketGateway({
  cors: {
    origin: '*',
    credentials: true,
  },
  namespace: '/hanuman',
  transports: ['websocket', 'polling'],
})
export class HanumanBridgeGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(HanumanBridgeGateway.name);
  private connectedAgents = new Map<string, AgentSocket>();

  constructor(
    private readonly agentManager: AgentManagerService,
    private readonly taskOrchestrator: TaskOrchestratorService,
    private readonly eventEmitter: EventEmitter2,
    private readonly structuredLogger: StructuredLoggerService,
    private readonly prometheusService: PrometheusService,
  ) {}

  /**
   * Gestion des connexions d'agents
   */
  async handleConnection(client: AgentSocket) {
    this.logger.log(`New connection attempt: ${client.id}`);
    
    // Envoyer demande d'authentification
    client.emit('auth.required', {
      message: 'Please authenticate your agent',
      timestamp: new Date().toISOString(),
    });

    // Timeout d'authentification
    setTimeout(() => {
      if (!client.agentId) {
        this.logger.warn(`Authentication timeout for client: ${client.id}`);
        client.disconnect();
      }
    }, 30000); // 30 secondes
  }

  /**
   * Gestion des déconnexions
   */
  async handleDisconnect(client: AgentSocket) {
    if (client.agentId) {
      this.logger.log(`Agent disconnected: ${client.agentId}`);
      
      // Retirer de la liste des agents connectés
      this.connectedAgents.delete(client.agentId);
      
      // Notifier le gestionnaire d'agents
      await this.agentManager.handleAgentDisconnection(client.agentId);
      
      // Émettre événement de déconnexion
      this.eventEmitter.emit('hanuman.agent.disconnected', {
        agentId: client.agentId,
        agentType: client.agentType,
        timestamp: new Date(),
      });

      // Métriques Prometheus
      this.prometheusService.recordHanumanEvent('agent_disconnected', {
        agent_type: client.agentType || 'unknown',
      });

      // Log structuré
      this.structuredLogger.logBusinessEvent(
        'agent_disconnected',
        'hanuman_agent',
        client.agentId,
        'disconnect',
        'success',
        { agentType: client.agentType }
      );
    }
  }

  /**
   * Authentification d'agent
   */
  @SubscribeMessage('auth.agent')
  async handleAgentAuth(
    @ConnectedSocket() client: AgentSocket,
    @MessageBody() payload: {
      agentId: string;
      agentType: string;
      capabilities: string[];
      version: string;
      apiKey?: string;
    }
  ) {
    try {
      const { agentId, agentType, capabilities, version, apiKey } = payload;

      // Valider l'authentification
      const isValid = await this.agentManager.validateAgent(agentId, agentType, apiKey);
      if (!isValid) {
        client.emit('auth.failed', {
          error: 'Invalid agent credentials',
          timestamp: new Date().toISOString(),
        });
        client.disconnect();
        return;
      }

      // Configurer le client
      client.agentId = agentId;
      client.agentType = agentType;
      client.capabilities = capabilities;
      client.lastHeartbeat = new Date();

      // Ajouter à la liste des agents connectés
      this.connectedAgents.set(agentId, client);

      // Enregistrer l'agent
      await this.agentManager.registerAgent({
        agentId,
        agentType,
        capabilities,
        version,
        status: 'connected',
        lastSeen: new Date(),
        socketId: client.id,
      });

      // Confirmer l'authentification
      client.emit('auth.success', {
        agentId,
        assignedTasks: await this.taskOrchestrator.getAssignedTasks(agentId),
        configuration: await this.agentManager.getAgentConfiguration(agentType),
        timestamp: new Date().toISOString(),
      });

      // Joindre la room du type d'agent
      client.join(`agents.${agentType}`);
      client.join(`agent.${agentId}`);

      // Émettre événement de connexion
      this.eventEmitter.emit('hanuman.agent.connected', {
        agentId,
        agentType,
        capabilities,
        timestamp: new Date(),
      });

      // Métriques et logs
      this.prometheusService.recordHanumanEvent('agent_connected', {
        agent_type: agentType,
      });

      this.structuredLogger.logBusinessEvent(
        'agent_connected',
        'hanuman_agent',
        agentId,
        'connect',
        'success',
        { agentType, capabilities }
      );

      this.logger.log(`Agent authenticated: ${agentId} (${agentType})`);

    } catch (error) {
      this.logger.error(`Agent authentication failed: ${error.message}`);
      client.emit('auth.failed', {
        error: 'Authentication failed',
        timestamp: new Date().toISOString(),
      });
      client.disconnect();
    }
  }

  /**
   * Heartbeat des agents
   */
  @SubscribeMessage('heartbeat')
  async handleHeartbeat(
    @ConnectedSocket() client: AgentSocket,
    @MessageBody() payload: { status: string; metrics?: any }
  ) {
    if (!client.agentId) {
      client.disconnect();
      return;
    }

    client.lastHeartbeat = new Date();
    
    // Mettre à jour le statut de l'agent
    await this.agentManager.updateAgentStatus(client.agentId, payload.status, payload.metrics);

    // Répondre au heartbeat
    client.emit('heartbeat.ack', {
      timestamp: new Date().toISOString(),
      nextHeartbeat: 30000, // 30 secondes
    });
  }

  /**
   * Exécution de tâche
   */
  @SubscribeMessage('task.execute')
  async handleTaskExecution(
    @ConnectedSocket() client: AgentSocket,
    @MessageBody() payload: {
      taskId: string;
      command: string;
      parameters: any;
      priority?: number;
    }
  ) {
    if (!client.agentId) {
      client.emit('error', { message: 'Agent not authenticated' });
      return;
    }

    try {
      const { taskId, command, parameters, priority = 1 } = payload;

      // Valider la tâche
      const isValid = await this.taskOrchestrator.validateTask(
        client.agentId,
        command,
        parameters
      );

      if (!isValid) {
        client.emit('task.error', {
          taskId,
          error: 'Invalid task or insufficient permissions',
          timestamp: new Date().toISOString(),
        });
        return;
      }

      // Enregistrer la tâche
      const task = await this.taskOrchestrator.createTask({
        taskId,
        agentId: client.agentId,
        command,
        parameters,
        priority,
        status: 'executing',
        createdAt: new Date(),
      });

      // Confirmer le début d'exécution
      client.emit('task.started', {
        taskId,
        estimatedDuration: task.estimatedDuration,
        timestamp: new Date().toISOString(),
      });

      // Log et métriques
      this.structuredLogger.logBusinessEvent(
        'task_started',
        'hanuman_task',
        taskId,
        'execute',
        'started',
        { agentId: client.agentId, command }
      );

      this.prometheusService.recordHanumanEvent('task_started', {
        agent_type: client.agentType,
        command,
      });

    } catch (error) {
      this.logger.error(`Task execution failed: ${error.message}`);
      client.emit('task.error', {
        taskId: payload.taskId,
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Résultat de tâche
   */
  @SubscribeMessage('task.result')
  async handleTaskResult(
    @ConnectedSocket() client: AgentSocket,
    @MessageBody() payload: {
      taskId: string;
      status: 'completed' | 'failed' | 'cancelled';
      result?: any;
      error?: string;
      metrics?: any;
    }
  ) {
    if (!client.agentId) return;

    try {
      const { taskId, status, result, error, metrics } = payload;

      // Mettre à jour la tâche
      await this.taskOrchestrator.updateTaskResult(taskId, {
        status,
        result,
        error,
        metrics,
        completedAt: new Date(),
      });

      // Émettre événement de completion
      this.eventEmitter.emit('hanuman.task.completed', {
        taskId,
        agentId: client.agentId,
        status,
        result,
        timestamp: new Date(),
      });

      // Confirmer la réception
      client.emit('task.acknowledged', {
        taskId,
        timestamp: new Date().toISOString(),
      });

      // Logs et métriques
      this.structuredLogger.logBusinessEvent(
        'task_completed',
        'hanuman_task',
        taskId,
        'complete',
        status,
        { agentId: client.agentId, hasError: !!error }
      );

      this.prometheusService.recordHanumanEvent('task_completed', {
        agent_type: client.agentType,
        status,
      });

    } catch (error) {
      this.logger.error(`Task result handling failed: ${error.message}`);
    }
  }

  /**
   * Rapport d'analyse
   */
  @SubscribeMessage('analysis.report')
  async handleAnalysisReport(
    @ConnectedSocket() client: AgentSocket,
    @MessageBody() payload: {
      reportId: string;
      analysisType: string;
      findings: any[];
      recommendations: string[];
      severity: 'low' | 'medium' | 'high' | 'critical';
      metadata?: any;
    }
  ) {
    if (!client.agentId) return;

    try {
      // Stocker le rapport
      await this.agentManager.storeAnalysisReport({
        ...payload,
        agentId: client.agentId,
        timestamp: new Date(),
      });

      // Traiter selon la sévérité
      if (payload.severity === 'critical') {
        await this.handleCriticalFindings(payload, client.agentId);
      }

      // Confirmer la réception
      client.emit('analysis.acknowledged', {
        reportId: payload.reportId,
        status: 'processed',
        nextActions: await this.getNextActions(payload.analysisType, payload.severity),
        timestamp: new Date().toISOString(),
      });

      // Émettre événement
      this.eventEmitter.emit('hanuman.analysis.completed', {
        reportId: payload.reportId,
        agentId: client.agentId,
        analysisType: payload.analysisType,
        severity: payload.severity,
        findingsCount: payload.findings.length,
      });

    } catch (error) {
      this.logger.error(`Analysis report handling failed: ${error.message}`);
    }
  }

  /**
   * Diffuser un message à tous les agents d'un type
   */
  async broadcastToAgentType(agentType: string, event: string, data: any) {
    this.server.to(`agents.${agentType}`).emit(event, {
      ...data,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Envoyer un message à un agent spécifique
   */
  async sendToAgent(agentId: string, event: string, data: any) {
    this.server.to(`agent.${agentId}`).emit(event, {
      ...data,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Obtenir le statut de tous les agents connectés
   */
  getConnectedAgentsStatus() {
    const agents = Array.from(this.connectedAgents.entries()).map(([agentId, socket]) => ({
      agentId,
      agentType: socket.agentType,
      capabilities: socket.capabilities,
      connected: socket.connected,
      lastHeartbeat: socket.lastHeartbeat,
      socketId: socket.id,
    }));

    return {
      totalAgents: agents.length,
      agentsByType: this.groupAgentsByType(agents),
      agents,
    };
  }

  /**
   * Méthodes privées
   */
  private groupAgentsByType(agents: any[]) {
    return agents.reduce((acc, agent) => {
      acc[agent.agentType] = (acc[agent.agentType] || 0) + 1;
      return acc;
    }, {});
  }

  private async handleCriticalFindings(payload: any, agentId: string) {
    // Notifier immédiatement les administrateurs
    this.eventEmitter.emit('hanuman.critical.alert', {
      agentId,
      findings: payload.findings,
      severity: payload.severity,
      timestamp: new Date(),
    });

    // Déclencher des actions automatiques si nécessaire
    await this.taskOrchestrator.triggerEmergencyResponse(payload);
  }

  private async getNextActions(analysisType: string, severity: string): Promise<string[]> {
    // Retourner les prochaines actions recommandées
    const actionMap = {
      security: {
        critical: ['immediate_lockdown', 'notify_security_team', 'audit_logs'],
        high: ['investigate_threat', 'update_security_rules'],
        medium: ['schedule_security_review'],
        low: ['log_for_analysis'],
      },
      performance: {
        critical: ['scale_resources', 'notify_devops'],
        high: ['optimize_queries', 'check_resources'],
        medium: ['schedule_optimization'],
        low: ['monitor_trends'],
      },
    };

    return actionMap[analysisType]?.[severity] || ['review_manually'];
  }
}
